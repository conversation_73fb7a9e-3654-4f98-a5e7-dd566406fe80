#!/usr/bin/env python3
"""
Comprehensive test to verify all verification history fixes are working
"""

import sqlite3
import datetime
import json
import sys
import os

# Add current directory to path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_staff_dashboard_today_verification():
    """Test staff dashboard today's verification history"""
    print("=== Testing Staff Dashboard Today's Verification History ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a staff member with verification data
        cursor.execute('''
            SELECT DISTINCT s.id, s.staff_id, s.full_name
            FROM staff s
            INNER JOIN biometric_verifications bv ON s.id = bv.staff_id
            LIMIT 1
        ''')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff with verification data found")
            return False
            
        staff_id = staff['id']
        today = datetime.date.today()
        
        print(f"Testing with staff: {staff['full_name']} (ID: {staff_id})")
        
        # Simulate get_today_attendance_status route
        # Get today's attendance
        attendance = cursor.execute('''
            SELECT time_in, time_out, overtime_in, overtime_out, status
            FROM attendance
            WHERE staff_id = ? AND date = ?
        ''', (staff_id, today)).fetchone()
        
        # Get today's verifications
        verifications = cursor.execute('''
            SELECT verification_type, verification_time, biometric_method, verification_status
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) = ?
            ORDER BY verification_time DESC
        ''', (staff_id, today)).fetchall()
        
        print(f"✅ Today's attendance: {'Found' if attendance else 'Not found'}")
        print(f"✅ Today's verifications: {len(verifications)} records")
        
        if verifications:
            print("Recent verifications:")
            for i, v in enumerate(verifications[:3]):
                print(f"  {i+1}. {v['verification_time']}: {v['verification_type']} - {v['verification_status']}")
        
        # Simulate the response
        response = {
            'success': True,
            'attendance': dict(attendance) if attendance else None,
            'verifications': [dict(v) for v in verifications],
            'available_actions': []
        }
        
        print(f"✅ Route response would be valid with {len(response['verifications'])} verifications")
        return len(response['verifications']) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

def test_staff_profile_recent_verifications():
    """Test staff profile recent biometric verifications"""
    print("\n=== Testing Staff Profile Recent Biometric Verifications ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a staff member with verification data
        cursor.execute('''
            SELECT DISTINCT s.id, s.staff_id, s.full_name
            FROM staff s
            INNER JOIN biometric_verifications bv ON s.id = bv.staff_id
            LIMIT 1
        ''')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff with verification data found")
            return False
            
        staff_id = staff['id']
        print(f"Testing with staff: {staff['full_name']} (ID: {staff_id})")
        
        # Simulate staff_my_profile route
        recent_verifications = cursor.execute('''
            SELECT verification_type, verification_time, biometric_method, verification_status
            FROM biometric_verifications
            WHERE staff_id = ?
            ORDER BY verification_time DESC
            LIMIT 20
        ''', (staff_id,)).fetchall()
        
        print(f"✅ Recent verifications: {len(recent_verifications)} records")
        
        if recent_verifications:
            print("Sample verifications:")
            for i, v in enumerate(recent_verifications[:5]):
                print(f"  {i+1}. {v['verification_time']}: {v['verification_type']} - {v['verification_status']}")
        
        return len(recent_verifications) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

def test_admin_staff_profile_modal():
    """Test admin dashboard staff profile modal"""
    print("\n=== Testing Admin Dashboard Staff Profile Modal ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a staff member with verification data
        cursor.execute('''
            SELECT DISTINCT s.id, s.staff_id, s.full_name
            FROM staff s
            INNER JOIN biometric_verifications bv ON s.id = bv.staff_id
            LIMIT 1
        ''')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff with verification data found")
            return False
            
        staff_id = staff['id']
        print(f"Testing with staff: {staff['full_name']} (ID: {staff_id})")
        
        # Simulate get_comprehensive_staff_profile route
        thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).date()
        
        # Get staff information
        staff_info = cursor.execute('''
            SELECT s.*, sc.name as school_name
            FROM staff s
            LEFT JOIN schools sc ON s.school_id = sc.id
            WHERE s.id = ?
        ''', (staff_id,)).fetchone()
        
        # Get biometric verifications (last 30 days)
        verifications = cursor.execute('''
            SELECT verification_type, verification_time, verification_status, device_ip
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) >= ?
            ORDER BY verification_time DESC
            LIMIT 50
        ''', (staff_id, thirty_days_ago)).fetchall()
        
        print(f"✅ Staff info: {'Found' if staff_info else 'Not found'}")
        print(f"✅ Verifications (last 30 days): {len(verifications)} records")
        
        if verifications:
            print("Sample verifications:")
            for i, v in enumerate(verifications[:5]):
                print(f"  {i+1}. {v['verification_time']}: {v['verification_type']} - {v['verification_status']}")
        
        # Simulate the response
        response = {
            'success': True,
            'staff': dict(staff_info) if staff_info else None,
            'verifications': [dict(v) for v in verifications],
            'attendance_stats': {'total_days': 1, 'present_days': 1}
        }
        
        print(f"✅ Modal would display {len(response['verifications'])} verifications")
        return len(response['verifications']) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

def test_javascript_functions():
    """Test if JavaScript functions are properly defined"""
    print("\n=== Testing JavaScript Functions ===")
    
    js_files = [
        ('static/js/staff_dashboard.js', ['updateVerificationHistory', 'loadTodayAttendanceStatus']),
        ('static/js/admin_dashboard.js', ['loadComprehensiveStaffProfile', 'renderComprehensiveStaffProfile'])
    ]
    
    all_functions_found = True
    
    for js_file, functions in js_files:
        if os.path.exists(js_file):
            print(f"✅ {js_file} exists")
            
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for func in functions:
                if func in content:
                    print(f"  ✅ {func} function found")
                else:
                    print(f"  ❌ {func} function NOT found")
                    all_functions_found = False
        else:
            print(f"❌ {js_file} does not exist")
            all_functions_found = False
    
    return all_functions_found

def test_template_filters():
    """Test if template filters are working"""
    print("\n=== Testing Template Filters ===")
    
    # Check if app.py has the required filters
    if os.path.exists('app.py'):
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        filters = ['datetimeformat', 'timeformat', 'dateformat']
        all_filters_found = True
        
        for filter_name in filters:
            if f"@app.template_filter('{filter_name}')" in content:
                print(f"✅ {filter_name} filter found")
            else:
                print(f"❌ {filter_name} filter NOT found")
                all_filters_found = False
        
        return all_filters_found
    else:
        print("❌ app.py not found")
        return False

if __name__ == "__main__":
    print("🔍 Comprehensive Verification History Fixes Test")
    print("=" * 60)
    
    results = []
    
    # Test all components
    results.append(("Staff Dashboard Today's Verification", test_staff_dashboard_today_verification()))
    results.append(("Staff Profile Recent Verifications", test_staff_profile_recent_verifications()))
    results.append(("Admin Staff Profile Modal", test_admin_staff_profile_modal()))
    results.append(("JavaScript Functions", test_javascript_functions()))
    results.append(("Template Filters", test_template_filters()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL VERIFICATION HISTORY ISSUES HAVE BEEN FIXED!")
        print("\nThe following should now be working:")
        print("1. ✅ Staff Dashboard - Today's Verification History")
        print("2. ✅ Staff Dashboard - Recent Biometric Verifications")
        print("3. ✅ Admin Dashboard - Staff Profile View")
        print("4. ✅ JavaScript Functions")
        print("5. ✅ Template Filters")
        print("\nIf you're still experiencing issues, they might be:")
        print("- Browser cache (try hard refresh: Ctrl+F5)")
        print("- Session/authentication issues")
        print("- Network connectivity issues")
    else:
        print(f"\n⚠️  {total - passed} issues still need attention")
        print("Check the failed tests above for details.")
