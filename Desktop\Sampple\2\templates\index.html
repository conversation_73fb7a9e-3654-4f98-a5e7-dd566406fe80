<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Registration & ESSL Fingerprint System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 2rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px;
        }
        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="mb-0">
                                <i class="fas fa-fingerprint me-2"></i>
                                User Registration & ESSL System
                            </h2>
                            <p class="mb-0 mt-2">Register users and manage fingerprint enrollment</p>
                        </div>
                        <div class="card-body p-4">
                            <form id="schoolForm">
                                <div class="mb-4">
                                    <label for="school_id" class="form-label fw-bold">Select Organization</label>
                                    <select class="form-select" id="school_id" name="school_id" required>
                                        <option value="">Choose an organization...</option>
                                        {% for school in schools %}
                                        <option value="{{ school.id }}">{{ school.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-primary" onclick="goToRegistration()">
                                        <i class="fas fa-user-plus me-2"></i>
                                        Register New User
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="viewStaffList()">
                                        <i class="fas fa-users me-2"></i>
                                        View Staff List
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    <script>
        function goToRegistration() {
            const schoolId = document.getElementById('school_id').value;
            if (!schoolId) {
                alert('Please select an organization first');
                return;
            }
            window.location.href = `/register?school_id=${schoolId}`;
        }

        function viewStaffList() {
            const schoolId = document.getElementById('school_id').value;
            if (!schoolId) {
                alert('Please select an organization first');
                return;
            }
            
            fetch(`/get_staff_list?school_id=${schoolId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Response is not JSON');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showStaffList(data.staff);
                    } else {
                        alert('Error loading staff list: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Staff list error:', error);
                    alert('Error: ' + error.message);
                });
        }

        function showStaffList(staff) {
            let html = '<div class="modal fade" id="staffModal" tabindex="-1">';
            html += '<div class="modal-dialog modal-lg">';
            html += '<div class="modal-content">';
            html += '<div class="modal-header">';
            html += '<h5 class="modal-title">Staff List</h5>';
            html += '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>';
            html += '</div>';
            html += '<div class="modal-body">';
            html += '<div class="table-responsive">';
            html += '<table class="table table-striped">';
            html += '<thead><tr><th>Staff ID</th><th>Name</th><th>Department</th><th>Biometric Status</th></tr></thead>';
            html += '<tbody>';
            
            staff.forEach(member => {
                const biometricStatus = member.biometric_enrolled ? 
                    '<span class="badge bg-success">Enrolled</span>' : 
                    '<span class="badge bg-warning">Not Enrolled</span>';
                html += `<tr>
                    <td>${member.staff_id}</td>
                    <td>${member.full_name}</td>
                    <td>${member.department || 'N/A'}</td>
                    <td>${biometricStatus}</td>
                </tr>`;
            });
            
            html += '</tbody></table></div></div></div></div></div>';
            
            document.body.insertAdjacentHTML('beforeend', html);
            const modal = new bootstrap.Modal(document.getElementById('staffModal'));
            modal.show();
            
            // Remove modal from DOM when hidden
            document.getElementById('staffModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }
    </script>
</body>
</html>
