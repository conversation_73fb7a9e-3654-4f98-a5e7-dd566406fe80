<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESSL Fingerprint Enrollment System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
            max-width: 500px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 2rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn-success {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px;
            font-size: 1.1rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .fingerprint-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .status-message {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 10px;
            display: none;
        }
        .status-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <div class="fingerprint-icon">
                                <i class="fas fa-fingerprint"></i>
                            </div>
                            <h2 class="mb-0">ESSL Fingerprint Enrollment</h2>
                            <p class="mb-0 mt-2">Enter Staff ID and Device IP to enroll fingerprint</p>
                        </div>
                        <div class="card-body p-4">
                            <form id="enrollmentForm">
                                <div class="mb-4">
                                    <label for="staff_id" class="form-label fw-bold">Staff ID</label>
                                    <input type="text" class="form-control" id="staff_id" name="staff_id"
                                           placeholder="Enter Staff ID" required>
                                </div>

                                <div class="mb-4">
                                    <label for="device_ip" class="form-label fw-bold">Device IP Address</label>
                                    <input type="text" class="form-control" id="device_ip" name="device_ip"
                                           value="*************" placeholder="*************" required>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-fingerprint me-2"></i>
                                        Enroll Fingerprint
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="testVerification()">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Test Verification
                                    </button>
                                </div>
                            </form>

                            <div id="statusMessage" class="status-message"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    <script>
        function showStatus(message, isSuccess = true) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status-message ${isSuccess ? 'status-success' : 'status-error'}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';

            // Hide after 5 seconds
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }

        document.getElementById('enrollmentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const staffId = document.getElementById('staff_id').value.trim();
            const deviceIp = document.getElementById('device_ip').value.trim();

            if (!staffId) {
                showStatus('Please enter a Staff ID', false);
                return;
            }

            if (!deviceIp) {
                showStatus('Please enter Device IP Address', false);
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enrolling...';
            submitBtn.disabled = true;

            const formData = new FormData();
            formData.append('staff_id', staffId);
            formData.append('device_ip', deviceIp);

            fetch('/enroll_biometric', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Response is not JSON');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showStatus(`✓ ${data.message}`, true);
                } else {
                    showStatus(`✗ ${data.error}`, false);
                }
            })
            .catch(error => {
                console.error('Enrollment error:', error);
                showStatus(`✗ Error: ${error.message}`, false);
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        function testVerification() {
            const staffId = document.getElementById('staff_id').value.trim();
            const deviceIp = document.getElementById('device_ip').value.trim();

            if (!staffId) {
                showStatus('Please enter a Staff ID first', false);
                return;
            }

            if (!deviceIp) {
                showStatus('Please enter Device IP Address first', false);
                return;
            }

            const formData = new FormData();
            formData.append('staff_id', staffId);
            formData.append('device_ip', deviceIp);

            fetch('/verify_biometric', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Response is not JSON');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showStatus(`✓ Verification successful: ${data.message}`, true);
                } else {
                    showStatus(`✗ Verification failed: ${data.error}`, false);
                }
            })
            .catch(error => {
                console.error('Verification error:', error);
                showStatus(`✗ Error: ${error.message}`, false);
            });
        }
    </script>
</body>
</html>
