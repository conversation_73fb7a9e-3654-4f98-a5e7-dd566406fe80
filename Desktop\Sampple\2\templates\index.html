<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VishnoRex - Staff Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <img src="{{ url_for('static', filename='images/logo.png') }}"
                             alt="Logo" class="mb-3 logo-height" id="schoolLogo">
                        <h5 class="mb-0">Staff Attendance System</h5>
                    </div>
                    <div class="card-body">
                        <form id="schoolLoginForm">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div class="mb-3">
                                <label for="schoolSelect" class="form-label">Select Institution</label>
                                <select class="form-select" id="schoolSelect" name="school_id" required>
                                    <option value="" selected disabled>Select your institution</option>
                                    {% for school in schools %}
                                    <option value="{{ school.id }}" data-logo="{{ url_for('static', filename=school.logo_url) if school.logo_url else '' }}">
                                        {{ school.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div id="loginFields" class="hidden">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" name="username" placeholder="Enter your username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">Login</button>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center">
                        <a href="/company_login" class="btn btn-link">Company Admin Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Inline Script for logo and form toggle -->
    <script>
        document.getElementById('schoolSelect').addEventListener('change', function () {
            const selectedOption = this.options[this.selectedIndex];
            const logoUrl = selectedOption.dataset.logo;
            const logoElement = document.getElementById('schoolLogo');

            // Show login fields only if a school is selected
            document.getElementById('loginFields').style.display = this.value ? 'block' : 'none';

            // Update logo based on selected school
            if (logoUrl) {
                logoElement.src = logoUrl;
                logoElement.onerror = function () {
                    this.src = '{{ url_for("static", filename="images/default_logo.png") }}';
                };
            } else {
                logoElement.src = '{{ url_for("static", filename="images/logo.png") }}';
            }
        });
    </script>

    <!-- Optional custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
