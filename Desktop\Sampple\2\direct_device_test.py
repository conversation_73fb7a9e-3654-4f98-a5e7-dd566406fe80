#!/usr/bin/env python3
"""
Direct Device Connection Test
Test ZK device connection without cloud interference
"""

def test_device_connection(device_ip, port=4370, device_id="1"):
    """Test direct device connection"""
    print(f"🔧 Testing Device Connection")
    print(f"IP: {device_ip}, Port: {port}, Device ID: {device_id}")
    print("-" * 50)
    
    try:
        # Import here to avoid cloud conflicts
        from zk_biometric import ZKBiometricDevice
        
        print("Creating ZKBiometricDevice...")
        device = ZKBiometricDevice(
            device_ip=device_ip,
            port=port,
            timeout=15,
            device_id=device_id,
            use_cloud=False
        )
        
        print(f"✅ Device object created")
        print(f"   Device IP: {device.device_ip}")
        print(f"   Port: {device.port}")
        print(f"   Device ID: {device.device_id}")
        print(f"   Use Cloud: {device.use_cloud}")
        
        print("\nAttempting connection...")
        if device.connect():
            print("✅ SUCCESS! Device connected")
            
            try:
                print("Getting users...")
                users = device.get_users()
                print(f"✅ Found {len(users)} users")
                
                print("Getting attendance records...")
                records = device.get_attendance_records()
                print(f"✅ Found {len(records)} attendance records")
                
                print("Disconnecting...")
                device.disconnect()
                print("✅ Disconnected successfully")
                
                return True
                
            except Exception as e:
                print(f"⚠️ Connected but error getting data: {e}")
                try:
                    device.disconnect()
                except:
                    pass
                return True  # Still a successful connection
                
        else:
            print("❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_configurations():
    """Test all possible device configurations"""
    print("🔍 TESTING ALL DEVICE CONFIGURATIONS")
    print("=" * 60)
    
    configurations = [
        # Most likely working configurations first
        {"ip": "*************", "port": 32150, "device_id": "181"},
        {"ip": "*************", "port": 4370, "device_id": "1"},
        {"ip": "*************", "port": 4370, "device_id": "1"},
        {"ip": "*************", "port": 32150, "device_id": "1"},
        {"ip": "*************", "port": 32150, "device_id": "181"},
    ]
    
    working_configs = []
    
    for i, config in enumerate(configurations, 1):
        print(f"\n📡 Configuration {i}/{len(configurations)}")
        print(f"Testing {config['ip']}:{config['port']} (Device ID: {config['device_id']})")
        
        if test_device_connection(config['ip'], config['port'], config['device_id']):
            working_configs.append(config)
            print(f"🎉 WORKING CONFIGURATION FOUND!")
            
            # Ask if user wants to continue testing or use this one
            print(f"\nFound working configuration:")
            print(f"  IP: {config['ip']}")
            print(f"  Port: {config['port']}")
            print(f"  Device ID: {config['device_id']}")
            
            # For now, continue testing to find all working configs
            
        else:
            print(f"❌ Configuration failed")
    
    return working_configs

def update_app_with_working_config(config):
    """Update app.py with the working configuration"""
    print(f"\n📝 UPDATING APP.PY WITH WORKING CONFIGURATION")
    print("-" * 50)
    
    print(f"Working configuration:")
    print(f"  IP: {config['ip']}")
    print(f"  Port: {config['port']}")
    print(f"  Device ID: {config['device_id']}")
    
    try:
        # Read app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        with open('app_backup.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Created backup: app_backup.py")
        
        # Update default device IP in the test_biometric_connection route
        import re
        
        # Update the specific line in test_biometric_connection
        pattern = r"device_ip = request\.form\.get\('device_ip', '[^']*'\)"
        replacement = f"device_ip = request.form.get('device_ip', '{config['ip']}')"
        content = re.sub(pattern, replacement, content)
        
        # Update other default IPs
        patterns_to_update = [
            (r"device_ip = request\.args\.get\('device_ip', '[^']*'\)", 
             f"device_ip = request.args.get('device_ip', '{config['ip']}')"),
            (r"device_ip = '[^']*'  # Default device IP",
             f"device_ip = '{config['ip']}'  # Default device IP"),
        ]
        
        for pattern, repl in patterns_to_update:
            content = re.sub(pattern, repl, content)
        
        # Save updated app.py
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Updated app.py with working device configuration")
        return True
        
    except Exception as e:
        print(f"❌ Error updating app.py: {e}")
        return False

def create_working_config_file(config):
    """Create a configuration file with working settings"""
    config_content = f"""# Working ZK Device Configuration
# Generated by direct_device_test.py

DEVICE_IP = "{config['ip']}"
DEVICE_PORT = {config['port']}
DEVICE_ID = "{config['device_id']}"
TIMEOUT = 15
USE_CLOUD = False

# Test command:
# python -c "from zk_biometric import ZKBiometricDevice; d=ZKBiometricDevice('{config['ip']}', {config['port']}, 15, '{config['device_id']}', False); print('Connected!' if d.connect() else 'Failed'); d.disconnect() if d.connect() else None"
"""
    
    try:
        with open('working_device_config.py', 'w') as f:
            f.write(config_content)
        print("✅ Created working_device_config.py")
    except Exception as e:
        print(f"❌ Error creating config file: {e}")

def main():
    """Main test function"""
    print("🔧 DIRECT DEVICE CONNECTION TEST")
    print("=" * 60)
    print("Testing ZK device connection to fix Ethernet connection error")
    print("=" * 60)
    
    # Test all configurations
    working_configs = test_all_configurations()
    
    if working_configs:
        print(f"\n🎉 SUCCESS! Found {len(working_configs)} working configurations:")
        
        for i, config in enumerate(working_configs, 1):
            print(f"{i}. {config['ip']}:{config['port']} (Device ID: {config['device_id']})")
        
        # Use the first working configuration
        best_config = working_configs[0]
        print(f"\n✅ Using configuration: {best_config['ip']}:{best_config['port']}")
        
        # Update app.py
        if update_app_with_working_config(best_config):
            print("✅ App updated successfully")
        
        # Create config file
        create_working_config_file(best_config)
        
        print(f"\n📋 NEXT STEPS:")
        print("1. ✅ App.py has been updated with working configuration")
        print("2. 🔄 Restart your Flask application")
        print("3. 🧪 Test biometric connection in web interface")
        print("4. 🎉 Connection should now work!")
        
    else:
        print(f"\n❌ No working configurations found")
        print("Device needs to be configured via web interface:")
        print("1. Go to: http://*************")
        print("2. Login: admin/123456")
        print("3. Configure Communication/Network settings:")
        print("   - Device ID: 181")
        print("   - Common Key: 1302")
        print("   - Port: 32150")
        print("   - TCP/IP: Enable")
        print("4. Save and restart device")
        print("5. Run this test again")

if __name__ == '__main__':
    main()
