#!/usr/bin/env python3
"""
Script to test biometric attendance sync functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from zk_biometric import ZKBiometricDevice
import sqlite3
from datetime import datetime

def test_device_connection():
    """Test connection to biometric device"""
    device_ip = '*************'  # Default IP
    
    print(f"Testing connection to biometric device at {device_ip}...")
    
    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            print("✅ Successfully connected to biometric device")
            
            # Test getting attendance records
            print("Fetching attendance records...")
            records = zk_device.get_attendance_records()
            
            if records:
                print(f"✅ Found {len(records)} attendance records")
                
                # Show sample records
                print("\nSample records:")
                for i, record in enumerate(records[:5]):  # Show first 5 records
                    print(f"  {i+1}. User ID: {record.get('user_id')}, "
                          f"Time: {record.get('timestamp')}, "
                          f"Type: {record.get('verification_type', 'unknown')}")
                
                if len(records) > 5:
                    print(f"  ... and {len(records) - 5} more records")
            else:
                print("⚠️ No attendance records found on device")
            
            zk_device.disconnect()
            return True
            
        else:
            print("❌ Failed to connect to biometric device")
            return False
            
    except Exception as e:
        print(f"❌ Error connecting to device: {e}")
        return False

def test_staff_mapping():
    """Test if staff IDs in database match biometric user IDs"""
    
    print("\nTesting staff ID mapping...")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get all staff
        cursor.execute('SELECT id, staff_id, full_name, school_id FROM staff')
        staff_records = cursor.fetchall()
        
        print(f"Found {len(staff_records)} staff members in database:")
        for staff in staff_records:
            print(f"  - DB ID: {staff['id']}, Staff ID: {staff['staff_id']}, "
                  f"Name: {staff['full_name']}, School: {staff['school_id']}")
        
        return staff_records
        
    except Exception as e:
        print(f"❌ Error getting staff records: {e}")
        return []
    finally:
        conn.close()

def test_attendance_sync():
    """Test the attendance sync process"""
    
    print("\nTesting attendance sync process...")
    
    # Test device connection first
    if not test_device_connection():
        print("❌ Cannot proceed with sync test - device connection failed")
        return
    
    # Test staff mapping
    staff_records = test_staff_mapping()
    if not staff_records:
        print("❌ Cannot proceed with sync test - no staff records found")
        return
    
    print("\n✅ All prerequisites met for attendance sync")
    print("\nTo test sync in the web app:")
    print("1. Login as admin")
    print("2. Go to admin dashboard")
    print("3. Look for 'Sync Attendance' or 'Biometric Device' section")
    print("4. Click 'Sync Attendance' button")
    print("5. Check if attendance records are updated")

def check_attendance_table():
    """Check current attendance records"""
    
    print("\nChecking current attendance records...")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get recent attendance records
        cursor.execute('''
            SELECT a.*, s.full_name, s.staff_id 
            FROM attendance a
            JOIN staff s ON a.staff_id = s.id
            ORDER BY a.date DESC, a.time_in DESC
            LIMIT 10
        ''')
        attendance_records = cursor.fetchall()
        
        if attendance_records:
            print(f"Found {len(attendance_records)} recent attendance records:")
            for record in attendance_records:
                print(f"  - {record['full_name']} (Staff ID: {record['staff_id']}) - "
                      f"Date: {record['date']}, In: {record['time_in']}, "
                      f"Out: {record['time_out']}, Status: {record['status']}")
        else:
            print("⚠️ No attendance records found in database")
            
    except Exception as e:
        print(f"❌ Error checking attendance records: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    print("=== Biometric Attendance Sync Test ===\n")
    
    # Run all tests
    test_device_connection()
    test_staff_mapping()
    check_attendance_table()
    test_attendance_sync()
    
    print("\n=== Summary ===")
    print("If all tests pass, the sync attendance feature should work properly.")
    print("If there are issues:")
    print("1. Check device IP address (currently set to *************)")
    print("2. Ensure device is connected to network")
    print("3. Verify staff IDs in database match biometric user IDs")
    print("4. Check that staff have been enrolled in the biometric device")
