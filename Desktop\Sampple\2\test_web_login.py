#!/usr/bin/env python3
"""
Test web login process by simulating the exact Flask login flow
"""

import requests
import json

def test_web_login(staff_id, password, school_id=4, base_url='http://127.0.0.1:5000'):
    """Test login through the web interface"""
    
    print(f"=== Testing Web Login for Staff ID: {staff_id} ===")
    print(f"URL: {base_url}")
    print(f"School ID: {school_id}")
    print(f"Staff ID: {staff_id}")
    print(f"Password: {password}")
    print()
    
    try:
        # Create a session to maintain cookies
        session = requests.Session()
        
        # Step 1: Get the login page to establish session
        print("Step 1: Getting login page...")
        response = session.get(base_url)
        if response.status_code == 200:
            print("✅ Successfully loaded main page")
        else:
            print(f"❌ Failed to load main page: {response.status_code}")
            return False
        
        # Step 2: Attempt login
        print("\nStep 2: Attempting login...")
        login_data = {
            'school_id': school_id,
            'username': staff_id,
            'password': password
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        print(f"Response status: {login_response.status_code}")
        print(f"Response headers: {dict(login_response.headers)}")
        
        try:
            response_json = login_response.json()
            print(f"Response JSON: {response_json}")
            
            if 'redirect' in response_json:
                print(f"✅ LOGIN SUCCESS: Redirect to {response_json['redirect']}")
                
                # Step 3: Test accessing the staff dashboard
                print("\nStep 3: Testing staff dashboard access...")
                dashboard_response = session.get(f"{base_url}{response_json['redirect']}")
                
                if dashboard_response.status_code == 200:
                    print("✅ Successfully accessed staff dashboard")
                    return True
                else:
                    print(f"❌ Failed to access dashboard: {dashboard_response.status_code}")
                    return False
                    
            elif 'error' in response_json:
                print(f"❌ LOGIN FAILED: {response_json['error']}")
                return False
            else:
                print(f"❌ Unexpected response: {response_json}")
                return False
                
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON response: {login_response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask app. Make sure it's running on http://127.0.0.1:5000")
        return False
    except Exception as e:
        print(f"❌ Error during web login test: {e}")
        return False

def check_flask_app_status(base_url='http://127.0.0.1:5000'):
    """Check if Flask app is running"""
    
    print("=== Checking Flask App Status ===")
    
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print(f"✅ Flask app is running at {base_url}")
            return True
        else:
            print(f"❌ Flask app returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ Flask app is not running at {base_url}")
        print("   Start it with: python app.py")
        return False
    except Exception as e:
        print(f"❌ Error checking Flask app: {e}")
        return False

if __name__ == '__main__':
    print("=== Web Login Test Tool ===\n")
    
    # Check if Flask app is running
    if not check_flask_app_status():
        print("\nPlease start the Flask app first:")
        print("python app.py")
        exit(1)
    
    print("\n" + "="*50)
    
    # Test login for both staff members
    staff_to_test = [
        ('888', 'Mohan'),
        ('333', 'Navanee')
    ]
    
    for staff_id, name in staff_to_test:
        print(f"\nTesting {name} (Staff ID: {staff_id})")
        success = test_web_login(staff_id, 'password123')
        print("-" * 50)
        
        if not success:
            print(f"\n🔍 DEBUGGING TIPS for {name}:")
            print("1. Check Flask app logs for error messages")
            print("2. Verify the staff ID is correct")
            print("3. Make sure you're selecting the right school")
            print("4. Try refreshing the browser and clearing cookies")
            print("5. Check browser developer tools for JavaScript errors")
    
    print("\n" + "="*50)
    print("NEXT STEPS:")
    print("1. If web login fails but database test passes, check Flask app logs")
    print("2. Look for any JavaScript errors in browser console")
    print("3. Verify the login form is submitting correct data")
    print("4. Check if there are any session or CSRF token issues")
