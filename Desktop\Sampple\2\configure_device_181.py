#!/usr/bin/env python3
"""
ZK Biometric Device 181 Configuration Script
Configure the system for your specific device:
- Device ID: 181
- Common Key: 1302
- Server: *************:32150
- Connection: PC/Ethernet
"""

import os
import json
import sys

def print_header():
    """Print configuration header"""
    print("=" * 70)
    print("🔧 ZK BIOMETRIC DEVICE 181 CONFIGURATION")
    print("=" * 70)
    print("Configuring system for your device:")
    print("🆔 Device ID: 181")
    print("🔑 Common Key: 1302")
    print("📡 Server: *************:32150")
    print("🔌 Connection: PC/Ethernet")
    print()

def configure_device_181():
    """Configure device 181 with common key 1302"""
    print("📱 Step 1: Configuring Device 181")
    print("-" * 40)
    
    try:
        from cloud_config import CloudConfigManager, DeviceConfig
        
        config_manager = CloudConfigManager()
        
        # Remove existing devices
        config_manager.devices = []
        
        # Add device 181 configuration
        device = DeviceConfig(
            device_id="181",
            device_name="ZK Biometric Device 181",
            device_type="ZK_BIOMETRIC",
            local_ip="*************",
            local_port=32150,
            cloud_enabled=True,
            sync_interval=30
        )
        
        # Add device
        success = config_manager.add_device(device)
        
        if success:
            print("✅ Device 181 configured successfully")
            print(f"   - Device ID: {device.device_id}")
            print(f"   - Device Name: {device.device_name}")
            print(f"   - Server IP: {device.local_ip}")
            print(f"   - Server Port: {device.local_port}")
            print(f"   - Cloud Enabled: {device.cloud_enabled}")
            print(f"   - Common Key: 1302 (will be used for connection)")
            return True
        else:
            print("❌ Failed to add device configuration")
            return False
            
    except ImportError:
        print("⚠️  Cloud modules not available")
        return False
    except Exception as e:
        print(f"❌ Error configuring device: {e}")
        return False

def update_environment_config():
    """Update environment configuration for device 181"""
    print("\n⚙️ Step 2: Updating Environment Configuration")
    print("-" * 40)
    
    env_content = f"""# ZK Biometric Device 181 Configuration
# Device-specific settings

# Flask Application Settings
SECRET_KEY=zk_device_181_secret_key_1302
FLASK_ENV=development
FLASK_DEBUG=true

# Device 181 Specific Settings
DEVICE_ID=181
DEVICE_COMMON_KEY=1302
DEVICE_NAME=ZK_Biometric_Device_181

# Cloud Service Configuration
CLOUD_API_BASE_URL=http://*************:32150
CLOUD_WEBSOCKET_URL=ws://*************:32150
CLOUD_MQTT_BROKER=*************
CLOUD_MQTT_PORT=32150

# Authentication (Update with your actual credentials)
CLOUD_API_KEY=device_181_api_key_1302
CLOUD_SECRET_KEY=device_181_secret_key_1302
CLOUD_ORG_ID=device_181_org

# Connection Settings for Device 181
CLOUD_USE_SSL=false
CLOUD_VERIFY_SSL=false
CLOUD_CONNECTION_TIMEOUT=30
CLOUD_RETRY_ATTEMPTS=5
CLOUD_HEARTBEAT_INTERVAL=60

# Sync Settings
CLOUD_AUTO_SYNC=true
CLOUD_SYNC_INTERVAL=30
CLOUD_BATCH_SIZE=100

# Device Configuration
DEFAULT_DEVICE_IP=*************
DEFAULT_DEVICE_PORT=32150
DEFAULT_DEVICE_ID=181
DEFAULT_COMMON_KEY=1302
DEFAULT_DEVICE_TIMEOUT=10

# Network Configuration
NETWORK_INTERFACE=auto
NETWORK_SUBNET=*************
NETWORK_GATEWAY=************

# Performance Settings
MAX_CONCURRENT_CONNECTIONS=50
REQUEST_TIMEOUT=30
MAX_RETRY_ATTEMPTS=5
QUEUE_MAX_SIZE=1000

# Feature Flags
ENABLE_CLOUD_FEATURES=true
ENABLE_WEBSOCKET=true
ENABLE_MQTT=false
ENABLE_API_RATE_LIMITING=true
ENABLE_REQUEST_LOGGING=true
ENABLE_PC_CONNECTION=true

# Development Settings
DEV_MOCK_DEVICES=false
DEV_SKIP_DEVICE_VALIDATION=false
DEV_ENABLE_DEBUG_ROUTES=true
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ Environment configuration updated")
        print("   - Device ID: 181")
        print("   - Common Key: 1302")
        print("   - Server: *************:32150")
        print("   - PC Connection enabled")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update environment: {e}")
        return False

def test_device_connection():
    """Test connection to device 181"""
    print("\n🔍 Step 3: Testing Device 181 Connection")
    print("-" * 40)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        print("Testing PC/Ethernet connection to device 181...")
        print(f"Connecting to: *************:32150")
        print(f"Device ID: 181")
        print(f"Common Key: 1302")
        
        # Test connection with device-specific settings
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=15,  # Longer timeout for network connection
            device_id='181',
            use_cloud=False  # Test direct connection first
        )
        
        print("Attempting to connect...")
        
        if device.connect():
            print("✅ Successfully connected to device 181!")
            
            try:
                # Try to get device info
                users = device.get_users()
                print(f"✅ Device is responding - Found {len(users)} users")
                
                # Try to get attendance records
                records = device.get_attendance_records()
                print(f"✅ Found {len(records)} attendance records")
                
                print("✅ Device 181 is fully operational!")
                
            except Exception as e:
                print(f"⚠️  Connected but limited functionality: {e}")
                print("💡 This might be due to permissions or device settings")
            
            device.disconnect()
            return True
            
        else:
            print("❌ Failed to connect to device 181")
            print("💡 Troubleshooting steps:")
            print("   1. Verify device is powered on")
            print("   2. Check network connectivity to *************")
            print("   3. Ensure port 32150 is accessible")
            print("   4. Verify device ID 181 is correct")
            print("   5. Check if common key 1302 is properly configured")
            return False
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def create_device_specific_scripts():
    """Create device-specific scripts"""
    print("\n📝 Step 4: Creating Device-Specific Scripts")
    print("-" * 40)
    
    # Create startup script for device 181
    startup_script = '''@echo off
echo ================================================
echo ZK Biometric Device 181 - Startup Script
echo ================================================
echo Device ID: 181
echo Common Key: 1302
echo Server: *************:32150
echo Connection: PC/Ethernet
echo ================================================
echo.

echo Configuring device 181...
python configure_device_181.py

echo.
echo Starting Flask application...
python app.py

pause
'''
    
    # Create test script for device 181
    test_script = '''#!/usr/bin/env python3
"""
Device 181 Connection Test Script
"""

from zk_biometric import ZKBiometricDevice
import time

def test_device_181():
    print("Testing ZK Biometric Device 181")
    print("Device ID: 181")
    print("Common Key: 1302")
    print("Server: *************:32150")
    print()
    
    device = ZKBiometricDevice(
        device_ip='*************',
        port=32150,
        timeout=15,
        device_id='181',
        use_cloud=False
    )
    
    print("Connecting...")
    if device.connect():
        print("✅ Connected successfully!")
        
        try:
            users = device.get_users()
            print(f"Users: {len(users)}")
            
            records = device.get_attendance_records()
            print(f"Records: {len(records)}")
            
        except Exception as e:
            print(f"Error getting data: {e}")
        
        device.disconnect()
    else:
        print("❌ Connection failed")

if __name__ == '__main__':
    test_device_181()
'''
    
    try:
        # Save startup script
        with open('start_device_181.bat', 'w') as f:
            f.write(startup_script)
        
        # Save test script
        with open('test_device_181.py', 'w') as f:
            f.write(test_script)
        
        print("✅ Created device-specific scripts:")
        print("   - start_device_181.bat (startup script)")
        print("   - test_device_181.py (connection test)")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create scripts: {e}")
        return False

def create_device_documentation():
    """Create documentation for device 181"""
    print("\n📚 Step 5: Creating Device Documentation")
    print("-" * 40)
    
    doc_content = """# ZK Biometric Device 181 Configuration

## Device Details
- **Device ID:** 181
- **Common Key:** 1302
- **Server IP:** *************
- **Server Port:** 32150
- **Connection Type:** PC/Ethernet

## Quick Start

### 1. Start the System
```bash
# Option 1: Use startup script
start_device_181.bat

# Option 2: Manual start
python app.py
```

### 2. Access Web Interface
- **Main Application:** http://localhost:5000
- **Cloud Dashboard:** http://localhost:5000/cloud_dashboard

### 3. Test Device Connection
```bash
# Test device 181 specifically
python test_device_181.py

# Or use the configuration script
python configure_device_181.py
```

## Device Configuration

### Connection Settings
```python
device = ZKBiometricDevice(
    device_ip='*************',
    port=32150,
    device_id='181',
    timeout=15
)
```

### Common Key Usage
The common key (1302) is used for:
- Device authentication
- Secure communication
- Data encryption
- Access control

## Troubleshooting

### Connection Issues
1. **Check Network Connectivity**
   ```bash
   ping *************
   ```

2. **Verify Port Access**
   ```bash
   telnet ************* 32150
   ```

3. **Test Device Response**
   ```bash
   python test_device_181.py
   ```

### Common Problems
- **Timeout Errors:** Increase timeout value or check network
- **Authentication Errors:** Verify common key 1302 is correct
- **Permission Errors:** Check device access permissions
- **Network Errors:** Verify IP ************* is reachable

## Configuration Files
- `.env` - Environment variables
- `cloud_config.json` - Device settings
- `configure_device_181.py` - Device-specific configuration

## Support
- Check system logs for detailed error messages
- Use debug mode for troubleshooting
- Test with minimal configuration first
"""
    
    try:
        with open('DEVICE_181_CONFIG.md', 'w') as f:
            f.write(doc_content)
        
        print("✅ Created device documentation: DEVICE_181_CONFIG.md")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create documentation: {e}")
        return False

def main():
    """Main configuration function"""
    print_header()
    
    print("🚀 Starting device 181 configuration...")
    print()
    
    # Run configuration steps
    steps = [
        ("Configure Device 181", configure_device_181),
        ("Update Environment Config", update_environment_config),
        ("Test Device Connection", test_device_connection),
        ("Create Device Scripts", create_device_specific_scripts),
        ("Create Documentation", create_device_documentation)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
            else:
                print(f"⚠️  {step_name} completed with warnings")
        except Exception as e:
            print(f"❌ {step_name} failed: {e}")
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 DEVICE 181 CONFIGURATION SUMMARY")
    print("=" * 70)
    
    if success_count >= 4:
        print("🎉 Device 181 configuration completed successfully!")
        print()
        print("✅ Your device is now configured:")
        print("   🆔 Device ID: 181")
        print("   🔑 Common Key: 1302")
        print("   📡 Server: *************:32150")
        print("   🔌 Connection: PC/Ethernet")
        print()
        print("🚀 Quick Start:")
        print("1. Double-click: start_device_181.bat")
        print("2. Or run: python app.py")
        print("3. Access: http://localhost:5000")
        print()
        print("🧪 Testing:")
        print("- Test connection: python test_device_181.py")
        print("- Web interface: http://localhost:5000")
        print("- Cloud dashboard: http://localhost:5000/cloud_dashboard")
        
    else:
        print("⚠️  Configuration completed with some issues")
        print(f"   {success_count}/{len(steps)} steps successful")
        print()
        print("💡 You can still use the system, but please review any errors above")
    
    print("\n📚 Documentation:")
    print("   - DEVICE_181_CONFIG.md (device-specific guide)")
    print("   - SERVER_CONFIG.md (server configuration)")
    print("   - CLOUD_MIGRATION_GUIDE.md (complete guide)")

if __name__ == '__main__':
    main()
