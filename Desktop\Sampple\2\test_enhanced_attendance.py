#!/usr/bin/env python3
"""
Enhanced Shift and Attendance Management System - Test Suite

This script tests all the new features implemented for the enhanced attendance system:
1. Database schema enhancements
2. Shift management system
3. Enhanced attendance verification logic
4. Regularization request system
5. Admin dashboard functionality
6. Enhanced calendar integration
7. Notification system
"""

import sqlite3
import datetime
import sys
import os
from contextlib import contextmanager

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@contextmanager
def get_test_db():
    """Get a test database connection"""
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()


class TestShiftManager:
    """Test version of ShiftManager that works without Flask context"""

    def __init__(self):
        self.shift_definitions = self._load_shift_definitions()

    def _load_shift_definitions(self):
        """Load shift definitions from database"""
        with get_test_db() as db:
            shifts = db.execute('''
                SELECT shift_type, start_time, end_time, grace_period_minutes, description
                FROM shift_definitions
                WHERE is_active = 1
            ''').fetchall()

            shift_dict = {}
            for shift in shifts:
                shift_dict[shift['shift_type']] = {
                    'start_time': datetime.datetime.strptime(shift['start_time'], '%H:%M:%S').time(),
                    'end_time': datetime.datetime.strptime(shift['end_time'], '%H:%M:%S').time(),
                    'grace_period_minutes': shift['grace_period_minutes'],
                    'description': shift['description']
                }

            return shift_dict

    def get_shift_info(self, shift_type):
        """Get shift information for a given shift type"""
        return self.shift_definitions.get(shift_type)

    def get_all_shift_types(self):
        """Get all available shift types"""
        return self.shift_definitions

    def calculate_attendance_status(self, shift_type, check_in_time, check_out_time=None):
        """Calculate attendance status based on shift type and times"""
        shift_info = self.get_shift_info(shift_type)
        if not shift_info:
            shift_info = self.get_shift_info('general')

        if not shift_info:
            raise ValueError(f"Shift type '{shift_type}' not found and no default general shift available")

        start_time = shift_info['start_time']
        end_time = shift_info['end_time']
        grace_period = shift_info['grace_period_minutes']

        # Calculate grace period cutoff (9:20 AM + 10 minutes = 9:30 AM)
        grace_cutoff = datetime.datetime.combine(datetime.date.today(), start_time)
        grace_cutoff += datetime.timedelta(minutes=grace_period)
        grace_cutoff_time = grace_cutoff.time()

        result = {
            'status': 'present',
            'late_duration_minutes': 0,
            'early_departure_minutes': 0,
            'requires_regularization': False,
            'shift_start_time': start_time,
            'shift_end_time': end_time
        }

        # Check for late arrival
        if check_in_time > grace_cutoff_time:
            result['status'] = 'late'
            # Calculate late duration in minutes
            check_in_dt = datetime.datetime.combine(datetime.date.today(), check_in_time)
            grace_cutoff_dt = datetime.datetime.combine(datetime.date.today(), grace_cutoff_time)
            late_duration = check_in_dt - grace_cutoff_dt
            result['late_duration_minutes'] = int(late_duration.total_seconds() / 60)
            result['requires_regularization'] = True

        # Check for early departure (if check_out_time is provided)
        if check_out_time and check_out_time < end_time:
            if result['status'] == 'late':
                pass  # Don't change status but note early departure
            else:
                result['status'] = 'left_soon'

            # Calculate early departure duration in minutes
            check_out_dt = datetime.datetime.combine(datetime.date.today(), check_out_time)
            end_time_dt = datetime.datetime.combine(datetime.date.today(), end_time)
            early_duration = end_time_dt - check_out_dt
            result['early_departure_minutes'] = int(early_duration.total_seconds() / 60)
            result['requires_regularization'] = True

        return result

    def format_duration_text(self, minutes, duration_type='late'):
        """Format duration in minutes to human-readable text"""
        if minutes <= 0:
            return ""

        hours = minutes // 60
        mins = minutes % 60

        if hours > 0:
            if mins > 0:
                duration_str = f"{hours} hour{'s' if hours > 1 else ''} {mins} minute{'s' if mins > 1 else ''}"
            else:
                duration_str = f"{hours} hour{'s' if hours > 1 else ''}"
        else:
            duration_str = f"{mins} minute{'s' if mins > 1 else ''}"

        if duration_type == 'late':
            return f"Late by {duration_str}"
        elif duration_type == 'early':
            return f"Left {duration_str} soon"
        else:
            return duration_str


class EnhancedAttendanceTestSuite:
    """Test suite for enhanced attendance management system"""

    def __init__(self):
        self.test_results = []
        self.shift_manager = TestShiftManager()
        
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_database_schema(self):
        """Test 1: Database Schema Enhancements"""
        print("\n=== Testing Database Schema Enhancements ===")
        
        with get_test_db() as db:
            cursor = db.cursor()
            
            # Test shift_definitions table
            try:
                cursor.execute("SELECT * FROM shift_definitions LIMIT 1")
                self.log_test("Shift definitions table exists", True)
                
                # Check for required columns
                cursor.execute("PRAGMA table_info(shift_definitions)")
                columns = [col[1] for col in cursor.fetchall()]
                required_cols = ['shift_type', 'start_time', 'end_time', 'grace_period_minutes']
                missing_cols = [col for col in required_cols if col not in columns]
                
                if not missing_cols:
                    self.log_test("Shift definitions table has required columns", True)
                else:
                    self.log_test("Shift definitions table has required columns", False, f"Missing: {missing_cols}")
                    
            except sqlite3.Error as e:
                self.log_test("Shift definitions table exists", False, str(e))
            
            # Test attendance_regularization_requests table
            try:
                cursor.execute("SELECT * FROM attendance_regularization_requests LIMIT 1")
                self.log_test("Regularization requests table exists", True)
            except sqlite3.Error as e:
                self.log_test("Regularization requests table exists", False, str(e))
            
            # Test notifications table
            try:
                cursor.execute("SELECT * FROM notifications LIMIT 1")
                self.log_test("Notifications table exists", True)
            except sqlite3.Error as e:
                self.log_test("Notifications table exists", False, str(e))
            
            # Test enhanced attendance columns
            try:
                cursor.execute("PRAGMA table_info(attendance)")
                columns = [col[1] for col in cursor.fetchall()]
                enhanced_cols = ['late_duration_minutes', 'early_departure_minutes', 'regularization_requested']
                missing_cols = [col for col in enhanced_cols if col not in columns]
                
                if not missing_cols:
                    self.log_test("Attendance table has enhanced columns", True)
                else:
                    self.log_test("Attendance table has enhanced columns", False, f"Missing: {missing_cols}")
                    
            except sqlite3.Error as e:
                self.log_test("Attendance table enhanced columns", False, str(e))
    
    def test_shift_management(self):
        """Test 2: Shift Management System"""
        print("\n=== Testing Shift Management System ===")
        
        try:
            # Test shift definitions loading
            shift_definitions = self.shift_manager.get_all_shift_types()
            if 'general' in shift_definitions and 'overtime' in shift_definitions:
                self.log_test("Shift definitions loaded correctly", True)
            else:
                self.log_test("Shift definitions loaded correctly", False, f"Available shifts: {list(shift_definitions.keys())}")
            
            # Test general shift info
            general_shift = self.shift_manager.get_shift_info('general')
            if general_shift and general_shift['start_time'] == datetime.time(9, 20):
                self.log_test("General shift configuration correct", True)
            else:
                self.log_test("General shift configuration correct", False, f"General shift: {general_shift}")
            
            # Test overtime shift info
            overtime_shift = self.shift_manager.get_shift_info('overtime')
            if overtime_shift and overtime_shift['end_time'] == datetime.time(17, 30):
                self.log_test("Overtime shift configuration correct", True)
            else:
                self.log_test("Overtime shift configuration correct", False, f"Overtime shift: {overtime_shift}")
                
        except Exception as e:
            self.log_test("Shift management system", False, str(e))
    
    def test_attendance_calculation(self):
        """Test 3: Enhanced Attendance Verification Logic"""
        print("\n=== Testing Enhanced Attendance Verification Logic ===")
        
        try:
            # Test on-time arrival (within grace period)
            on_time_result = self.shift_manager.calculate_attendance_status(
                'general', datetime.time(9, 25)  # 5 minutes after start, within grace period
            )
            
            if on_time_result['status'] == 'present' and on_time_result['late_duration_minutes'] == 0:
                self.log_test("On-time arrival calculation", True)
            else:
                self.log_test("On-time arrival calculation", False, f"Result: {on_time_result}")
            
            # Test late arrival
            late_result = self.shift_manager.calculate_attendance_status(
                'general', datetime.time(9, 45)  # 25 minutes after start, 15 minutes late
            )
            
            if late_result['status'] == 'late' and late_result['late_duration_minutes'] == 15:
                self.log_test("Late arrival calculation", True)
            else:
                self.log_test("Late arrival calculation", False, f"Result: {late_result}")
            
            # Test early departure
            early_departure_result = self.shift_manager.calculate_attendance_status(
                'general', 
                datetime.time(9, 15),  # On time check-in
                datetime.time(16, 0)   # 30 minutes early departure
            )
            
            if early_departure_result['early_departure_minutes'] == 30:
                self.log_test("Early departure calculation", True)
            else:
                self.log_test("Early departure calculation", False, f"Result: {early_departure_result}")
            
            # Test duration formatting
            duration_text = self.shift_manager.format_duration_text(75, 'late')  # 1 hour 15 minutes
            if "1 hour 15 minutes" in duration_text:
                self.log_test("Duration text formatting", True)
            else:
                self.log_test("Duration text formatting", False, f"Got: {duration_text}")
                
        except Exception as e:
            self.log_test("Attendance calculation logic", False, str(e))
    
    def test_regularization_system(self):
        """Test 4: Regularization Request System"""
        print("\n=== Testing Regularization Request System ===")
        
        try:
            with get_test_db() as db:
                # Create a test staff member if not exists
                cursor = db.cursor()
                cursor.execute("SELECT id FROM staff WHERE staff_id = 'TEST001' LIMIT 1")
                staff = cursor.fetchone()
                
                if not staff:
                    cursor.execute("""
                        INSERT INTO staff (school_id, staff_id, password_hash, full_name, shift_type)
                        VALUES (1, 'TEST001', 'test', 'Test Staff', 'general')
                    """)
                    db.commit()
                    staff_id = cursor.lastrowid
                else:
                    staff_id = staff['id']
                
                # Create a test attendance record
                today = datetime.date.today()
                cursor.execute("""
                    INSERT OR REPLACE INTO attendance 
                    (staff_id, school_id, date, time_in, status, late_duration_minutes)
                    VALUES (?, 1, ?, '09:45:00', 'late', 15)
                """, (staff_id, today))
                attendance_id = cursor.lastrowid
                db.commit()
                
                # Test regularization request creation (manual SQL)
                cursor.execute('''
                    INSERT INTO attendance_regularization_requests
                    (attendance_id, staff_id, school_id, request_type, original_time,
                     expected_time, duration_minutes, staff_reason)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (attendance_id, staff_id, 1, 'late_arrival',
                      '09:45:00', '09:30:00', 15, 'Traffic jam'))

                request_id = cursor.lastrowid
                db.commit()

                if request_id:
                    self.log_test("Regularization request creation", True)
                else:
                    self.log_test("Regularization request creation", False)

                # Test getting pending requests
                pending_requests = cursor.execute('''
                    SELECT r.*, s.full_name, s.staff_id as staff_number, a.date, a.time_in, a.time_out
                    FROM attendance_regularization_requests r
                    JOIN staff s ON r.staff_id = s.id
                    JOIN attendance a ON r.attendance_id = a.id
                    WHERE r.school_id = ? AND r.status = 'pending'
                    ORDER BY r.requested_at DESC
                ''', (1,)).fetchall()

                if any(req['id'] == request_id for req in pending_requests):
                    self.log_test("Pending requests retrieval", True)
                else:
                    self.log_test("Pending requests retrieval", False)

                # Test request processing
                cursor.execute('''
                    UPDATE attendance_regularization_requests
                    SET status = ?, processed_by = ?, processed_at = ?, admin_reason = ?
                    WHERE id = ?
                ''', ('approved', 1, datetime.datetime.now(), 'Valid reason', request_id))

                cursor.execute('''
                    UPDATE attendance
                    SET regularization_status = ?
                    WHERE id = ?
                ''', ('approved', attendance_id))

                db.commit()

                if cursor.rowcount > 0:
                    self.log_test("Request processing", True)
                else:
                    self.log_test("Request processing", False)
                    
        except Exception as e:
            self.log_test("Regularization system", False, str(e))
    
    def test_notification_system(self):
        """Test 5: Notification System"""
        print("\n=== Testing Notification System ===")
        
        try:
            with get_test_db() as db:
                cursor = db.cursor()

                # Test notification creation (manual SQL)
                cursor.execute('''
                    INSERT INTO notifications (staff_id, school_id, title, message, notification_type)
                    VALUES (?, ?, ?, ?, ?)
                ''', (1, 1, "Test Notification", "This is a test notification", "general"))

                notification_id = cursor.lastrowid
                db.commit()

                if notification_id:
                    self.log_test("Notification creation", True)
                else:
                    self.log_test("Notification creation", False)

                # Test notification retrieval
                notifications = cursor.execute('''
                    SELECT * FROM notifications
                    WHERE staff_id = ?
                    ORDER BY created_at DESC
                ''', (1,)).fetchall()

                if any(notif['id'] == notification_id for notif in notifications):
                    self.log_test("Notification retrieval", True)
                else:
                    self.log_test("Notification retrieval", False)

                # Test marking notification as read
                cursor.execute('UPDATE notifications SET is_read = 1 WHERE id = ?', (notification_id,))
                db.commit()

                if cursor.rowcount > 0:
                    self.log_test("Mark notification as read", True)
                else:
                    self.log_test("Mark notification as read", False)

        except Exception as e:
            self.log_test("Notification system", False, str(e))
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Enhanced Attendance Management System Tests")
        print("=" * 60)
        
        self.test_database_schema()
        self.test_shift_management()
        self.test_attendance_calculation()
        self.test_regularization_system()
        self.test_notification_system()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['passed']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['message']}")
        
        return passed_tests == total_tests


if __name__ == "__main__":
    test_suite = EnhancedAttendanceTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! The enhanced attendance system is ready.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        sys.exit(1)
