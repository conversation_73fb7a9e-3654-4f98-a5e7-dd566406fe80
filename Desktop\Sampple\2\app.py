# Simplified Flask App for User Registration and ESSL Fingerprint Integration
from flask import Flask, render_template, request, redirect, url_for, session, jsonify, g
import sqlite3
from flask_wtf.csrf import CSRFProtect
from werkzeug.security import generate_password_hash, check_password_hash
import datetime
import os
import time
from database import get_db, init_db
from zk_biometric import ZKBiometricDevice, verify_staff_biometric

# Create Flask app instance
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', os.urandom(24).hex())

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Initialize database with the app
init_db(app)

# File upload configuration for user photos
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Database connection cleanup
@app.teardown_appcontext
def close_db(error):
    """Close database connection at the end of request"""
    _ = error  # Suppress unused parameter warning
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# Main index route - School selection
@app.route('/')
def index():
    db = get_db()
    schools = db.execute('SELECT id, name FROM schools ORDER BY name').fetchall()
    return render_template('index.html', schools=schools)

# User Registration Route
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'GET':
        db = get_db()
        schools = db.execute('SELECT id, name FROM schools ORDER BY name').fetchall()
        return render_template('register.html', schools=schools)
    
    # Handle POST request for registration
    school_id = request.form.get('school_id')
    staff_id = request.form.get('staff_id')
    full_name = request.form.get('full_name')
    email = request.form.get('email')
    phone = request.form.get('phone')
    department = request.form.get('department')
    position = request.form.get('position')
    password = request.form.get('password')
    
    # Validate required fields
    if not all([school_id, staff_id, full_name, password]):
        return jsonify({'success': False, 'error': 'All required fields must be filled'})
    
    db = get_db()
    
    try:
        # Check if staff_id already exists in this school
        existing = db.execute('''
            SELECT id FROM staff WHERE school_id = ? AND staff_id = ?
        ''', (school_id, staff_id)).fetchone()
        
        if existing:
            return jsonify({'success': False, 'error': 'Staff ID already exists in this school'})
        
        # Hash password
        password_hash = generate_password_hash(password)
        
        # Insert new staff member
        cursor = db.execute('''
            INSERT INTO staff (school_id, staff_id, full_name, email, phone, department, position, password_hash)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (school_id, staff_id, full_name, email, phone, department, position, password_hash))
        
        db.commit()
        
        return jsonify({
            'success': True, 
            'message': 'Registration successful! You can now enroll your biometric data.',
            'staff_db_id': cursor.lastrowid
        })
        
    except sqlite3.IntegrityError:
        return jsonify({'success': False, 'error': 'Staff ID already exists'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'Registration failed: {str(e)}'})

# Biometric Enrollment Route
@app.route('/enroll_biometric', methods=['POST'])
def enroll_biometric():
    staff_db_id = request.form.get('staff_db_id')
    device_ip = request.form.get('device_ip', '*************')
    
    if not staff_db_id:
        return jsonify({'success': False, 'error': 'Staff ID required'})
    
    db = get_db()
    
    # Get staff information
    staff = db.execute('SELECT staff_id, full_name FROM staff WHERE id = ?', (staff_db_id,)).fetchone()
    if not staff:
        return jsonify({'success': False, 'error': 'Staff not found'})
    
    try:
        # Initialize ZK device
        zk_device = ZKBiometricDevice(device_ip)
        
        if not zk_device.connect():
            return jsonify({'success': False, 'error': 'Failed to connect to biometric device'})
        
        # Enroll user on device
        result = zk_device.enroll_user(
            user_id=staff['staff_id'],
            name=staff['full_name'],
            overwrite=True
        )
        
        if result['success']:
            # Update database to mark as enrolled
            db.execute('''
                UPDATE staff SET biometric_enrolled = 1 WHERE id = ?
            ''', (staff_db_id,))
            db.commit()
            
            # Trigger biometric enrollment mode
            enrollment_result = zk_device.trigger_biometric_enrollment(staff['staff_id'])
            
            zk_device.disconnect()
            
            return jsonify({
                'success': True,
                'message': f'User enrolled successfully. {enrollment_result.get("message", "Please complete biometric enrollment on device.")}',
                'enrollment_mode': enrollment_result.get('manual_mode', True)
            })
        else:
            zk_device.disconnect()
            return jsonify({'success': False, 'error': result['message']})
            
    except Exception as e:
        return jsonify({'success': False, 'error': f'Enrollment error: {str(e)}'})

# Biometric Verification Route
@app.route('/verify_biometric', methods=['POST'])
def verify_biometric():
    staff_id = request.form.get('staff_id')
    device_ip = request.form.get('device_ip', '*************')
    
    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID required'})
    
    try:
        result = verify_staff_biometric(staff_id, device_ip)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': f'Verification error: {str(e)}'})

# Check Device Verification Route
@app.route('/check_device_verification', methods=['POST'])
def check_device_verification():
    staff_id = request.form.get('staff_id')
    device_ip = request.form.get('device_ip', '*************')
    
    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID required'})
    
    try:
        # Check for recent biometric verification from the device
        zk_device = ZKBiometricDevice(device_ip)
        
        if not zk_device.connect():
            return jsonify({'success': False, 'error': 'Failed to connect to biometric device'})
        
        # Look for recent attendance records for this staff member (within last 30 seconds)
        recent_cutoff = datetime.datetime.now() - datetime.timedelta(seconds=30)
        recent_records = zk_device.get_new_attendance_records(recent_cutoff)
        
        staff_recent_record = None
        for record in recent_records:
            if str(record['user_id']) == str(staff_id):
                staff_recent_record = record
                break
        
        zk_device.disconnect()
        
        if staff_recent_record:
            # Log the verification
            db = get_db()
            staff_info = db.execute('SELECT id, school_id FROM staff WHERE staff_id = ?', (staff_id,)).fetchone()
            
            if staff_info:
                db.execute('''
                    INSERT INTO biometric_verifications
                    (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
                    VALUES (?, ?, ?, ?, ?, 'fingerprint', 'success')
                ''', (staff_info['id'], staff_info['school_id'], staff_recent_record['verification_type'], 
                      staff_recent_record['timestamp'], device_ip))
                db.commit()
            
            return jsonify({
                'success': True,
                'message': f'Biometric verification successful: {staff_recent_record["verification_type"]}',
                'verification_type': staff_recent_record['verification_type'],
                'timestamp': staff_recent_record['timestamp'].strftime('%Y-%m-%d %I:%M %p')
            })
        else:
            return jsonify({
                'success': False,
                'error': 'No recent biometric verification found. Please use the biometric device.'
            })
            
    except Exception as e:
        return jsonify({'success': False, 'error': f'Verification check error: {str(e)}'})

# Get Staff List Route
@app.route('/get_staff_list')
def get_staff_list():
    school_id = request.args.get('school_id')
    if not school_id:
        return jsonify({'success': False, 'error': 'School ID required'})
    
    db = get_db()
    staff = db.execute('''
        SELECT id, staff_id, full_name, department, position, biometric_enrolled
        FROM staff 
        WHERE school_id = ?
        ORDER BY full_name
    ''', (school_id,)).fetchall()
    
    return jsonify({
        'success': True,
        'staff': [dict(s) for s in staff]
    })

# Add School Route (for initial setup)
@app.route('/add_school', methods=['POST'])
def add_school():
    name = request.form.get('name')
    address = request.form.get('address')
    contact_email = request.form.get('contact_email')
    contact_phone = request.form.get('contact_phone')
    
    if not name:
        return jsonify({'success': False, 'error': 'School name is required'})
    
    db = get_db()
    
    try:
        cursor = db.execute('''
            INSERT INTO schools (name, address, contact_email, contact_phone)
            VALUES (?, ?, ?, ?)
        ''', (name, address, contact_email, contact_phone))
        db.commit()
        
        return jsonify({
            'success': True,
            'message': 'School added successfully',
            'school_id': cursor.lastrowid
        })
    except Exception as e:
        return jsonify({'success': False, 'error': f'Failed to add school: {str(e)}'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)