#!/usr/bin/env python3
"""
ZK Biometric Device 181 - Advanced Troubleshooting Script
Diagnose and fix connection issues with Device ID 181
"""

import socket
import subprocess
import platform
import time
from zk_biometric import ZKBiometricDevice

def print_header():
    print("=" * 60)
    print("🔧 ZK BIOMETRIC DEVICE 181 - TROUBLESHOOTING")
    print("=" * 60)
    print("Device ID: 181")
    print("Common Key: 1302")
    print("Server: *************:32150")
    print("Connection: PC/Ethernet")
    print("=" * 60)

def test_basic_network():
    """Test basic network connectivity"""
    print("\n📡 Step 1: Testing Basic Network Connectivity")
    print("-" * 50)
    
    try:
        # Test ping
        print("Testing ping to *************...")
        if platform.system().lower() == "windows":
            result = subprocess.run(['ping', '-n', '4', '*************'], 
                                  capture_output=True, text=True, timeout=15)
        else:
            result = subprocess.run(['ping', '-c', '4', '*************'], 
                                  capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ Ping successful - Network connectivity OK")
            # Extract ping statistics
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if 'time=' in line.lower() or 'average' in line.lower():
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ Ping failed - Network connectivity issue")
            print(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Network test error: {e}")
        return False

def test_port_connectivity():
    """Test if port 32150 is accessible"""
    print("\n🔌 Step 2: Testing Port Connectivity")
    print("-" * 50)
    
    try:
        print("Testing TCP connection to *************:32150...")
        
        # Test socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        result = sock.connect_ex(('*************', 32150))
        sock.close()
        
        if result == 0:
            print("✅ Port 32150 is accessible")
            return True
        else:
            print("❌ Port 32150 is not accessible")
            print("   Possible causes:")
            print("   - Device is not powered on")
            print("   - Port is blocked by firewall")
            print("   - Device is not listening on this port")
            print("   - Wrong IP address or port")
            return False
            
    except Exception as e:
        print(f"❌ Port test error: {e}")
        return False

def test_zk_connection_basic():
    """Test basic ZK device connection"""
    print("\n🔗 Step 3: Testing ZK Device Connection (Basic)")
    print("-" * 50)
    
    try:
        print("Creating ZK device instance...")
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=20,  # Longer timeout
            device_id='181',
            use_cloud=False
        )
        
        print("Attempting connection...")
        if device.connect():
            print("✅ ZK device connection successful!")
            
            try:
                # Test basic device info
                print("Getting device information...")
                device_info = device.get_device_info()
                print(f"   Device Info: {device_info}")
                
                # Test user count
                users = device.get_users()
                print(f"   Users found: {len(users)}")
                
                # Test attendance records
                records = device.get_attendance_records()
                print(f"   Attendance records: {len(records)}")
                
                device.disconnect()
                return True
                
            except Exception as e:
                print(f"⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True  # Connection worked, just limited access
                
        else:
            print("❌ ZK device connection failed")
            return False
            
    except Exception as e:
        print(f"❌ ZK connection error: {e}")
        return False

def test_zk_connection_advanced():
    """Test ZK device connection with different parameters"""
    print("\n🔧 Step 4: Testing ZK Connection (Advanced)")
    print("-" * 50)
    
    # Test different port configurations
    test_configs = [
        {'port': 32150, 'timeout': 30, 'desc': 'Your configured port with extended timeout'},
        {'port': 4370, 'timeout': 15, 'desc': 'Standard ZK port'},
        {'port': 8080, 'timeout': 15, 'desc': 'Alternative web port'},
        {'port': 80, 'timeout': 15, 'desc': 'HTTP port'},
    ]
    
    for config in test_configs:
        print(f"\nTesting {config['desc']} (port {config['port']})...")
        
        try:
            device = ZKBiometricDevice(
                device_ip='*************',
                port=config['port'],
                timeout=config['timeout'],
                device_id='181',
                use_cloud=False
            )
            
            if device.connect():
                print(f"✅ SUCCESS: Connected on port {config['port']}")
                try:
                    users = device.get_users()
                    print(f"   Users: {len(users)}")
                    device.disconnect()
                    return config['port']  # Return successful port
                except:
                    device.disconnect()
                    return config['port']  # Connection worked
            else:
                print(f"❌ Failed on port {config['port']}")
                
        except Exception as e:
            print(f"❌ Error on port {config['port']}: {e}")
    
    return None

def test_device_discovery():
    """Try to discover ZK devices on the network"""
    print("\n🔍 Step 5: Device Discovery")
    print("-" * 50)
    
    print("Scanning for ZK devices on common ports...")
    
    # Common ZK device ports
    common_ports = [4370, 32150, 8080, 80, 443, 8000, 9000]
    
    # Test local network range (assuming 182.66.109.x)
    base_ip = "182.66.109."
    
    found_devices = []
    
    # Test the specific IP first
    print(f"Testing ************* on common ports...")
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('*************', port))
            sock.close()
            
            if result == 0:
                print(f"✅ Found service on *************:{port}")
                found_devices.append(f"*************:{port}")
        except:
            pass
    
    if found_devices:
        print(f"\n✅ Found {len(found_devices)} accessible services:")
        for device in found_devices:
            print(f"   - {device}")
        return found_devices
    else:
        print("❌ No accessible services found")
        return []

def provide_solutions():
    """Provide troubleshooting solutions"""
    print("\n💡 TROUBLESHOOTING SOLUTIONS")
    print("=" * 60)
    
    print("\n🔧 Common Solutions:")
    print("1. Device Power and Network:")
    print("   - Ensure device 181 is powered on")
    print("   - Check network cable connections")
    print("   - Verify device is on the same network")
    
    print("\n2. Device Configuration:")
    print("   - Verify device ID is actually 181")
    print("   - Check if common key 1302 is configured")
    print("   - Ensure device is in PC connection mode")
    
    print("\n3. Network Configuration:")
    print("   - Check if IP ************* is correct")
    print("   - Verify port 32150 is the right port")
    print("   - Check firewall settings")
    
    print("\n4. Device Settings:")
    print("   - Access device menu directly")
    print("   - Check communication settings")
    print("   - Verify Ethernet/TCP settings")
    
    print("\n🔍 Next Steps:")
    print("1. Check device display for IP settings")
    print("2. Try connecting via device's web interface")
    print("3. Use ZK device management software")
    print("4. Contact device administrator")

def main():
    """Main troubleshooting function"""
    print_header()
    
    # Run all tests
    tests = [
        ("Basic Network", test_basic_network),
        ("Port Connectivity", test_port_connectivity),
        ("ZK Connection Basic", test_zk_connection_basic),
        ("ZK Connection Advanced", test_zk_connection_advanced),
        ("Device Discovery", test_device_discovery)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TROUBLESHOOTING SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    # Provide recommendations
    if results.get("Basic Network"):
        if results.get("Port Connectivity"):
            if results.get("ZK Connection Basic"):
                print("\n🎉 SUCCESS: Device connection is working!")
            else:
                print("\n⚠️ Network OK but ZK connection failed")
                print("   Check device configuration and authentication")
        else:
            print("\n⚠️ Network OK but port not accessible")
            print("   Check device power and port configuration")
    else:
        print("\n❌ Network connectivity issue")
        print("   Check network connection and IP address")
    
    provide_solutions()

if __name__ == '__main__':
    main()
