/**
 * Monthly Attendance Calendar Component
 * Displays attendance data in a monthly calendar view
 */

class MonthlyAttendanceCalendar {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            staffId: options.staffId || null,
            isAdminView: options.isAdminView || false,
            ...options
        };
        
        this.currentMonth = new Date();
        this.monthlyData = [];
        this.statistics = {};
        
        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error('Monthly calendar container not found');
            return;
        }
        
        this.render();
        this.loadMonthlyData();
    }
    
    formatMonth(date) {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    }
    
    formatDate(date) {
        return date.toISOString().split('T')[0];
    }
    
    render() {
        const monthName = this.currentMonth.toLocaleDateString('en-US', { 
            month: 'long', 
            year: 'numeric' 
        });
        
        this.container.innerHTML = `
            <div class="monthly-calendar-container">
                <!-- Calendar Header -->
                <div class="calendar-header d-flex justify-content-between align-items-center mb-3">
                    <button class="btn btn-outline-primary btn-sm" id="prevMonth">
                        <i class="bi bi-chevron-left"></i> Previous
                    </button>
                    <h4 class="mb-0">${monthName}</h4>
                    <button class="btn btn-outline-primary btn-sm" id="nextMonth">
                        Next <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
                
                <!-- Statistics Summary -->
                <div class="row mb-3" id="monthlyStats">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Present</h5>
                                <h3 id="presentDays">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Absent</h5>
                                <h3 id="absentDays">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">On Duty</h5>
                                <h3 id="onDutyDays">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Attendance</h5>
                                <h3 id="attendanceRate">0%</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Calendar Grid -->
                <div class="calendar-grid">
                    <!-- Days of week header -->
                    <div class="calendar-header-row">
                        <div class="calendar-day-header">Mon</div>
                        <div class="calendar-day-header">Tue</div>
                        <div class="calendar-day-header">Wed</div>
                        <div class="calendar-day-header">Thu</div>
                        <div class="calendar-day-header">Fri</div>
                        <div class="calendar-day-header">Sat</div>
                        <div class="calendar-day-header">Sun</div>
                    </div>
                    
                    <!-- Calendar days -->
                    <div id="calendarDays" class="calendar-days">
                        <!-- Days will be populated here -->
                    </div>
                </div>
                
                <!-- Loading indicator -->
                <div id="loadingIndicator" class="text-center mt-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading attendance data...</p>
                </div>
            </div>
        `;
        
        // Add event listeners
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.currentMonth.setMonth(this.currentMonth.getMonth() - 1);
            this.render();
            this.loadMonthlyData();
        });
        
        document.getElementById('nextMonth').addEventListener('click', () => {
            this.currentMonth.setMonth(this.currentMonth.getMonth() + 1);
            this.render();
            this.loadMonthlyData();
        });
    }
    
    loadMonthlyData() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        loadingIndicator.style.display = 'block';
        
        const params = new URLSearchParams({
            month: this.formatMonth(this.currentMonth)
        });
        
        if (this.options.staffId) {
            params.append('staff_id', this.options.staffId);
        }
        
        fetch(`/get_monthly_attendance?${params}`)
            .then(response => response.json())
            .then(data => {
                loadingIndicator.style.display = 'none';
                
                if (data.success) {
                    this.monthlyData = data.monthly_data;
                    this.statistics = data.statistics;
                    this.updateCalendar();
                    this.updateStatistics();
                } else {
                    console.error('Failed to load monthly data:', data.error);
                    this.showError('Failed to load attendance data');
                }
            })
            .catch(error => {
                loadingIndicator.style.display = 'none';
                console.error('Error loading monthly data:', error);
                this.showError('Error loading attendance data');
            });
    }
    
    updateCalendar() {
        const calendarDays = document.getElementById('calendarDays');
        
        // Get first day of month and calculate calendar start
        const firstDay = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), 1);
        const lastDay = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 0);
        
        // Calculate calendar start (Monday of the week containing the first day)
        const calendarStart = new Date(firstDay);
        const dayOfWeek = firstDay.getDay();
        const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Adjust for Monday start
        calendarStart.setDate(firstDay.getDate() - daysToSubtract);
        
        // Generate calendar days (6 weeks = 42 days)
        let calendarHTML = '';
        const currentDate = new Date(calendarStart);
        
        for (let i = 0; i < 42; i++) {
            const dateStr = this.formatDate(currentDate);
            const dayNumber = currentDate.getDate();
            const isCurrentMonth = currentDate.getMonth() === this.currentMonth.getMonth();
            const isToday = dateStr === this.formatDate(new Date());
            
            // Find attendance data for this date
            const dayData = this.monthlyData.find(d => d.date === dateStr);
            
            calendarHTML += this.renderDayCell(currentDate, dayData, isCurrentMonth, isToday);
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        calendarDays.innerHTML = calendarHTML;
    }
    
    renderDayCell(date, dayData, isCurrentMonth, isToday) {
        const dayNumber = date.getDate();
        const dateStr = this.formatDate(date);
        
        let cellClass = 'calendar-day';
        if (!isCurrentMonth) cellClass += ' other-month';
        if (isToday) cellClass += ' today';
        
        let statusClass = 'status-absent';
        let statusText = 'Absent';
        let statusIcon = '⚪';
        let detailsHTML = '';
        
        if (dayData) {
            if (dayData.present_status === 'Present') {
                statusClass = 'status-present';
                statusText = 'Present';
                statusIcon = '🟢';
                detailsHTML = `
                    <div class="day-details">
                        <small>In: ${dayData.morning_thumb || '--:--'}</small>
                        <small>Out: ${dayData.evening_thumb || '--:--'}</small>
                    </div>
                `;
            } else if (dayData.present_status === 'On Duty') {
                statusClass = 'status-on-duty';
                statusText = 'On Duty';
                statusIcon = '🔵';
                detailsHTML = `
                    <div class="day-details">
                        <small>${dayData.on_duty_type || 'Official Work'}</small>
                        <small>${dayData.on_duty_location || 'Not specified'}</small>
                    </div>
                `;
            }
            
            if (dayData.delay_info) {
                statusIcon = '🟡';
                statusText = 'Late';
            }
        }
        
        return `
            <div class="calendar-day ${cellClass} ${statusClass}" data-date="${dateStr}">
                <div class="day-number">${dayNumber}</div>
                <div class="day-status">
                    <span class="status-icon">${statusIcon}</span>
                    <small class="status-text">${statusText}</small>
                </div>
                ${detailsHTML}
            </div>
        `;
    }
    
    updateStatistics() {
        if (this.statistics) {
            document.getElementById('presentDays').textContent = this.statistics.present_days || 0;
            document.getElementById('absentDays').textContent = this.statistics.absent_days || 0;
            document.getElementById('onDutyDays').textContent = this.statistics.on_duty_days || 0;
            document.getElementById('attendanceRate').textContent = `${this.statistics.attendance_rate || 0}%`;
        }
    }
    
    showError(message) {
        const calendarDays = document.getElementById('calendarDays');
        calendarDays.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle"></i> ${message}
            </div>
        `;
    }
    
    // Public methods
    setStaffId(staffId) {
        this.options.staffId = staffId;
        this.loadMonthlyData();
    }
    
    refresh() {
        this.loadMonthlyData();
    }
    
    goToMonth(year, month) {
        this.currentMonth = new Date(year, month - 1, 1);
        this.render();
        this.loadMonthlyData();
    }
}

// Export for use in other scripts
window.MonthlyAttendanceCalendar = MonthlyAttendanceCalendar;
