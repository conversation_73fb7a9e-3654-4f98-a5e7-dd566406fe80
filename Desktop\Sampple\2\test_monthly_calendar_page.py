#!/usr/bin/env python3
"""
Test the monthly calendar page directly
"""

import sys
sys.path.append('.')
from app import app

def test_page_functionality():
    """Test if the page actually works"""
    print("=== Testing Monthly Calendar Page ===")
    
    with app.test_client() as client:
        # Test with staff session
        with client.session_transaction() as sess:
            sess['user_id'] = 40
            sess['user_type'] = 'staff'
            sess['full_name'] = 'Test Staff'
            sess['school_id'] = 1
        
        try:
            response = client.get('/staff/monthly_calendar')
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                html_content = response.get_data(as_text=True)
                
                # Check for essential elements
                checks = [
                    ('<!DOCTYPE html>', 'Valid HTML5 doctype'),
                    ('<html lang="en">', 'HTML lang attribute'),
                    ('id="monthlyCalendar"', 'Calendar container'),
                    ('MonthlyAttendanceCalendar', 'Calendar class'),
                    ('refreshCalendar', 'Refresh function'),
                    ('printCalendar', 'Print function'),
                    ('goToCurrentMonth', 'Navigation function'),
                    ('</html>', 'Closing HTML tag'),
                    ('</body>', 'Closing body tag'),
                    ('</script>', 'Script tags closed')
                ]
                
                all_passed = True
                for check, description in checks:
                    if check in html_content:
                        print(f"✅ {description}")
                    else:
                        print(f"❌ {description}")
                        all_passed = False
                
                # Check if JavaScript variables are rendered
                if 'staffId:' in html_content and '{{ staff.id }}' not in html_content:
                    print("✅ Template variables rendered correctly")
                else:
                    print("❌ Template variables not rendered")
                    all_passed = False
                
                # Count script tags manually
                script_opens = html_content.count('<script')
                script_closes = html_content.count('</script>')
                
                print(f"\nScript tag analysis:")
                print(f"  <script tags: {script_opens}")
                print(f"  </script> tags: {script_closes}")
                
                # Check for self-closing script tags
                self_closing = 0
                lines = html_content.split('\n')
                for line in lines:
                    if '<script' in line and '</script>' in line:
                        self_closing += 1
                
                print(f"  Self-closing script tags: {self_closing}")
                
                # Calculate balance
                non_self_closing = script_opens - self_closing
                standalone_closing = script_closes - self_closing
                
                print(f"  Non-self-closing opening: {non_self_closing}")
                print(f"  Standalone closing: {standalone_closing}")
                print(f"  Balance: {'✅ CORRECT' if non_self_closing == standalone_closing else '❌ INCORRECT'}")
                
                return all_passed and (non_self_closing == standalone_closing)
            else:
                print(f"❌ Page failed to load: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing page: {e}")
            return False

def test_browser_rendering():
    """Test if the page would render correctly in a browser"""
    print("\n=== Testing Browser Rendering ===")
    
    try:
        with open('templates/staff_monthly_calendar.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for common HTML issues that would break rendering
        issues = []
        
        # Check for unclosed major tags
        major_tags = ['html', 'head', 'body', 'div', 'nav', 'script', 'style']
        
        for tag in major_tags:
            opening = content.count(f'<{tag}')
            closing = content.count(f'</{tag}>')
            
            # Special handling for self-closing tags
            if tag == 'script':
                # Count self-closing script tags
                self_closing = len([line for line in content.split('\n') 
                                 if f'<{tag}' in line and f'</{tag}>' in line])
                opening -= self_closing
                closing -= self_closing
            
            if opening != closing:
                issues.append(f"Mismatched {tag} tags: {opening} opening, {closing} closing")
        
        if issues:
            print("❌ HTML structure issues found:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ HTML structure appears valid")
            return True
            
    except Exception as e:
        print(f"❌ Error checking HTML structure: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Monthly Calendar Page Functionality")
    print("=" * 60)
    
    # Test page functionality
    page_works = test_page_functionality()
    
    # Test HTML structure
    html_valid = test_browser_rendering()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    if page_works and html_valid:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The monthly calendar page is working correctly")
        print("✅ HTML structure is valid")
        print("✅ JavaScript should load and execute properly")
        print("✅ Template variables are rendered correctly")
        
        print("\n🚀 The page should work perfectly in browsers!")
        print("\nIf you're still experiencing issues, they might be:")
        print("- Browser cache problems (clear cache)")
        print("- Network connectivity issues")
        print("- Browser-specific compatibility problems")
        print("- JavaScript disabled in browser")
        
    else:
        print("⚠️ SOME ISSUES FOUND")
        if not page_works:
            print("❌ Page functionality issues detected")
        if not html_valid:
            print("❌ HTML structure issues detected")
        
        print("\nPlease review the issues above and fix them.")
