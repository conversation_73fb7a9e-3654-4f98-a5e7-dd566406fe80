#!/usr/bin/env python3
"""
Ethernet Connection Diagnostic Tool
Helps diagnose ZK device Ethernet connectivity issues
"""

import socket
import subprocess
import sys
import time
import requests
from urllib.parse import urlparse

def test_network_connectivity(ip_address="*************"):
    """Test basic network connectivity"""
    print("🌐 Testing Network Connectivity")
    print("-" * 40)
    
    try:
        # Test ping
        result = subprocess.run(
            ["ping", "-n", "4", ip_address] if sys.platform == "win32" else ["ping", "-c", "4", ip_address],
            capture_output=True, text=True, timeout=30
        )
        
        if result.returncode == 0:
            print(f"✅ Ping successful to {ip_address}")
            # Extract average time if possible
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Average' in line or 'avg' in line:
                    print(f"   {line.strip()}")
            return True
        else:
            print(f"❌ Ping failed to {ip_address}")
            return False
            
    except Exception as e:
        print(f"❌ Network test error: {e}")
        return False

def test_port_connectivity(ip_address="*************", ports=[32150, 4370, 80, 443]):
    """Test connectivity to specific ports"""
    print(f"\n🔌 Testing Port Connectivity")
    print("-" * 40)
    
    results = {}
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((ip_address, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port}: OPEN")
                results[port] = True
            else:
                print(f"❌ Port {port}: CLOSED/FILTERED")
                results[port] = False
                
        except Exception as e:
            print(f"❌ Port {port}: ERROR - {e}")
            results[port] = False
    
    return results

def test_web_interface(ip_address="*************"):
    """Test web interface accessibility"""
    print(f"\n🌐 Testing Web Interface")
    print("-" * 40)
    
    urls_to_test = [
        f"http://{ip_address}",
        f"https://{ip_address}",
        f"http://{ip_address}:80",
        f"http://{ip_address}:8080"
    ]
    
    for url in urls_to_test:
        try:
            response = requests.get(url, timeout=10, verify=False)
            if response.status_code == 200:
                print(f"✅ {url}: Accessible")
                print(f"   Status: {response.status_code}")
                print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
                
                # Check for ZK-specific content
                content = response.text.lower()
                if any(keyword in content for keyword in ['zkteco', 'attendance', 'biometric', 'device']):
                    print(f"   🎯 Appears to be ZK device interface")
                
                return True
            else:
                print(f"⚠️ {url}: HTTP {response.status_code}")
                
        except requests.exceptions.SSLError:
            print(f"⚠️ {url}: SSL Certificate issue (device may still be accessible)")
        except requests.exceptions.ConnectTimeout:
            print(f"❌ {url}: Connection timeout")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url}: Connection refused")
        except Exception as e:
            print(f"❌ {url}: {str(e)}")
    
    return False

def test_zk_protocol_connection(ip_address="*************", port=32150):
    """Test raw socket connection to ZK protocol port"""
    print(f"\n🔧 Testing ZK Protocol Connection")
    print("-" * 40)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print(f"Attempting connection to {ip_address}:{port}...")
        result = sock.connect_ex((ip_address, port))
        
        if result == 0:
            print(f"✅ Socket connection successful")
            
            # Try to send a basic ZK command
            try:
                # Basic ZK protocol handshake attempt
                sock.send(b'\x50\x50\x82\x7d\x13\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
                sock.settimeout(5)
                response = sock.recv(1024)
                
                if response:
                    print(f"✅ Device responded with {len(response)} bytes")
                    print(f"   Response: {response.hex()}")
                    return True
                else:
                    print(f"⚠️ No response from device")
                    
            except socket.timeout:
                print(f"⚠️ Device connected but no response (may need authentication)")
            except Exception as e:
                print(f"⚠️ Protocol error: {e}")
            
            return True
            
        else:
            print(f"❌ Socket connection failed (error {result})")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def provide_recommendations(network_ok, ports_result, web_ok, zk_ok):
    """Provide recommendations based on test results"""
    print(f"\n📋 DIAGNOSTIC RESULTS & RECOMMENDATIONS")
    print("=" * 60)
    
    if not network_ok:
        print("❌ NETWORK ISSUE:")
        print("   - Device is not reachable via network")
        print("   - Check physical connection (Ethernet cable)")
        print("   - Verify device is powered on")
        print("   - Check network configuration")
        return
    
    if not ports_result.get(32150, False) and not ports_result.get(4370, False):
        print("❌ ZK PROTOCOL PORTS CLOSED:")
        print("   - Device is not listening on ZK protocol ports")
        print("   - Access web interface to enable TCP/IP communication")
        print("   - Check Communication settings in device menu")
        
    if web_ok:
        print("✅ WEB INTERFACE ACCESSIBLE:")
        print("   - Configure device via web interface")
        print("   - Go to System → Communication")
        print("   - Enable TCP/IP communication")
        print("   - Set Device ID to 181")
        print("   - Set Common Key to 1302")
        print("   - Set port to 32150")
        
    if zk_ok:
        print("✅ ZK PROTOCOL WORKING:")
        print("   - Device is responding to ZK protocol")
        print("   - Connection should work with proper authentication")
        
    print(f"\n🎯 NEXT STEPS:")
    if web_ok:
        print("1. Open web interface: http://*************")
        print("2. Login with admin credentials")
        print("3. Configure communication settings")
        print("4. Test connection again")
    else:
        print("1. Check device manual for default web interface settings")
        print("2. Try different ports or IP addresses")
        print("3. Contact device administrator")

def main():
    """Main diagnostic function"""
    print("🔧 ZK DEVICE ETHERNET CONNECTION DIAGNOSTIC")
    print("=" * 60)
    print("Device: *************")
    print("Target Port: 32150")
    print("=" * 60)
    
    # Run all tests
    network_ok = test_network_connectivity()
    ports_result = test_port_connectivity()
    web_ok = test_web_interface()
    zk_ok = test_zk_protocol_connection()
    
    # Provide recommendations
    provide_recommendations(network_ok, ports_result, web_ok, zk_ok)
    
    print(f"\n🔄 After making changes, test with:")
    print("   python simple_test_181.py")

if __name__ == '__main__':
    main()
