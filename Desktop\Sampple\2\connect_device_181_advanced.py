#!/usr/bin/env python3
"""
ZK Device 181 - Advanced Connection Methods
Try different authentication and connection methods
"""

from zk_biometric import ZKBiometricDevice
import time

def try_connection_method_1():
    """Standard connection with your settings"""
    print("🔗 Method 1: Standard Connection")
    print("-" * 40)
    
    try:
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=20,
            device_id='181',
            use_cloud=False
        )
        
        print("Connecting with Device ID 181, Port 32150...")
        if device.connect():
            print("✅ SUCCESS: Connected with standard method!")
            try:
                users = device.get_users()
                print(f"   Users: {len(users)}")
                device.disconnect()
                return True
            except Exception as e:
                print(f"   ⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
        else:
            print("❌ Failed: Standard connection")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def try_connection_method_2():
    """Try with standard ZK port"""
    print("\n🔗 Method 2: Standard ZK Port (4370)")
    print("-" * 40)
    
    try:
        device = ZKBiometricDevice(
            device_ip='*************',
            port=4370,
            timeout=20,
            device_id='181',
            use_cloud=False
        )
        
        print("Connecting with Device ID 181, Port 4370...")
        if device.connect():
            print("✅ SUCCESS: Connected with standard ZK port!")
            try:
                users = device.get_users()
                print(f"   Users: {len(users)}")
                device.disconnect()
                return True
            except Exception as e:
                print(f"   ⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
        else:
            print("❌ Failed: Standard ZK port")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def try_connection_method_3():
    """Try without device ID"""
    print("\n🔗 Method 3: Connection Without Device ID")
    print("-" * 40)
    
    try:
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=20,
            use_cloud=False
        )
        
        print("Connecting without specific Device ID...")
        if device.connect():
            print("✅ SUCCESS: Connected without device ID!")
            try:
                users = device.get_users()
                print(f"   Users: {len(users)}")
                device.disconnect()
                return True
            except Exception as e:
                print(f"   ⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
        else:
            print("❌ Failed: Connection without device ID")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def try_connection_method_4():
    """Try with different timeout and retry"""
    print("\n🔗 Method 4: Extended Timeout and Retry")
    print("-" * 40)
    
    for attempt in range(3):
        try:
            print(f"Attempt {attempt + 1}/3...")
            device = ZKBiometricDevice(
                device_ip='*************',
                port=32150,
                timeout=30,
                device_id='181',
                use_cloud=False
            )
            
            if device.connect():
                print("✅ SUCCESS: Connected with extended timeout!")
                try:
                    users = device.get_users()
                    print(f"   Users: {len(users)}")
                    device.disconnect()
                    return True
                except Exception as e:
                    print(f"   ⚠️ Connected but limited access: {e}")
                    device.disconnect()
                    return True
            else:
                print(f"❌ Attempt {attempt + 1} failed")
                time.sleep(2)
                
        except Exception as e:
            print(f"❌ Attempt {attempt + 1} error: {e}")
            time.sleep(2)
    
    return False

def try_web_based_connection():
    """Try to get data via web interface"""
    print("\n🌐 Method 5: Web-Based Data Access")
    print("-" * 40)
    
    import requests
    
    try:
        # Try to access device info via web
        response = requests.get("http://*************", timeout=10)
        if response.status_code == 200:
            print("✅ Web interface accessible")
            print("   You can manage the device via web interface")
            print("   URL: http://*************")
            
            # Check if we can find any API endpoints
            content = response.text
            if 'api' in content.lower():
                print("   🔍 API endpoints may be available")
            
            return True
        else:
            print("❌ Web interface not accessible")
            return False
            
    except Exception as e:
        print(f"❌ Web interface error: {e}")
        return False

def provide_manual_steps():
    """Provide manual configuration steps"""
    print("\n📋 MANUAL CONFIGURATION STEPS")
    print("=" * 50)
    
    print("1. 🌐 Access Web Interface:")
    print("   - Open: http://*************")
    print("   - Login with admin credentials")
    
    print("\n2. 🔧 Check Communication Settings:")
    print("   - Go to System → Communication")
    print("   - Verify TCP/IP is enabled")
    print("   - Check port number (should be 32150)")
    print("   - Verify device ID is 181")
    
    print("\n3. 🔐 Check Security Settings:")
    print("   - Look for 'Common Key' setting")
    print("   - Ensure it's set to 1302")
    print("   - Check if encryption is enabled")
    
    print("\n4. 🔄 Enable PC Connection Mode:")
    print("   - Look for 'Connection Mode' or 'Communication Mode'")
    print("   - Set to 'PC Connection' or 'TCP/IP'")
    print("   - Save settings and restart device if needed")
    
    print("\n5. 🧪 Test Connection:")
    print("   - After configuring, run: python test_device_181.py")
    print("   - Or use the web interface to test connectivity")

def main():
    """Main function to try all connection methods"""
    print("🔧 ZK DEVICE 181 - ADVANCED CONNECTION METHODS")
    print("=" * 60)
    print("Device: *************:32150")
    print("Device ID: 181, Common Key: 1302")
    print("=" * 60)
    
    methods = [
        ("Standard Connection", try_connection_method_1),
        ("Standard ZK Port", try_connection_method_2),
        ("Without Device ID", try_connection_method_3),
        ("Extended Timeout", try_connection_method_4),
        ("Web Interface", try_web_based_connection)
    ]
    
    success_count = 0
    
    for method_name, method_func in methods:
        try:
            if method_func():
                success_count += 1
                print(f"✅ {method_name}: SUCCESS")
            else:
                print(f"❌ {method_name}: FAILED")
        except Exception as e:
            print(f"❌ {method_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print("📊 CONNECTION ATTEMPT SUMMARY")
    print("=" * 60)
    print(f"Successful methods: {success_count}/{len(methods)}")
    
    if success_count > 0:
        print("🎉 At least one connection method worked!")
        print("Use the successful method for your application")
    else:
        print("⚠️ All automatic methods failed")
        print("Manual configuration via web interface required")
    
    provide_manual_steps()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Configure device via web interface: http://*************")
    print("2. Verify TCP/IP settings and device ID 181")
    print("3. Set common key to 1302")
    print("4. Enable PC connection mode")
    print("5. Test connection again")

if __name__ == '__main__':
    main()
