#!/usr/bin/env python3
"""
Test ZK Device Connection on Port 80
Since the device responds to ZK protocol on port 80, let's test it properly
"""

from zk_biometric import ZKBiometricDevice
import time

def test_port_80_configurations():
    """Test different configurations on port 80"""
    print("🔧 TESTING ZK DEVICE ON PORT 80")
    print("=" * 50)
    print("Device: *************:80")
    print("=" * 50)
    
    # Different configurations to try on port 80
    configs = [
        {"device_id": "1", "description": "Default Device ID"},
        {"device_id": "181", "description": "Your Device ID"},
        {"device_id": "0", "description": "No Device ID"},
        {"device_id": None, "description": "Auto Device ID"},
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n🔧 Configuration {i}: {config['description']}")
        print("-" * 40)
        
        try:
            device = ZKBiometricDevice(
                device_ip='*************',
                port=80,  # Use port 80 instead of 32150
                timeout=15,
                device_id=config['device_id'],
                use_cloud=False
            )
            
            print(f"Device ID: {config['device_id']}")
            print("Connecting...", end=" ")
            
            if device.connect():
                print("✅ SUCCESS!")
                
                try:
                    # Test basic operations
                    print("📊 Testing device operations:")
                    
                    # Get users
                    users = device.get_users()
                    print(f"   Users: {len(users)}")
                    
                    # Get attendance records
                    records = device.get_attendance_records()
                    print(f"   Attendance records: {len(records)}")
                    
                    # Get device info if possible
                    try:
                        device_info = device.get_device_info()
                        if device_info:
                            print(f"   Device info: {device_info}")
                    except:
                        print("   Device info: Not available")
                    
                    print(f"\n🎉 WORKING CONFIGURATION FOUND!")
                    print(f"   IP: *************")
                    print(f"   Port: 80")
                    print(f"   Device ID: {config['device_id']}")
                    
                    device.disconnect()
                    return config
                    
                except Exception as e:
                    print(f"   ⚠️ Connected but limited access: {e}")
                    device.disconnect()
                    return config  # Still a working connection
                    
            else:
                print("❌ Failed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return None

def test_port_80_with_different_timeouts():
    """Test port 80 with different timeout settings"""
    print(f"\n🕐 Testing different timeout settings on port 80...")
    print("-" * 50)
    
    timeouts = [5, 10, 15, 20, 30]
    
    for timeout in timeouts:
        print(f"Testing timeout {timeout}s...", end=" ")
        
        try:
            device = ZKBiometricDevice(
                device_ip='*************',
                port=80,
                timeout=timeout,
                device_id='1',
                use_cloud=False
            )
            
            if device.connect():
                print("✅ SUCCESS!")
                try:
                    users = device.get_users()
                    print(f"   Found {len(users)} users")
                    device.disconnect()
                    return timeout
                except Exception as e:
                    print(f"   Connected but error: {e}")
                    device.disconnect()
                    return timeout
            else:
                print("❌ Failed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return None

def create_working_config_file(config):
    """Create a configuration file with working settings"""
    if not config:
        return
    
    config_content = f"""# Working ZK Device Configuration
# Generated by test_port_80_connection.py

DEVICE_IP = "*************"
DEVICE_PORT = 80
DEVICE_ID = "{config['device_id']}"
TIMEOUT = 15
USE_CLOUD = False

# Usage example:
# from zk_biometric import ZKBiometricDevice
# device = ZKBiometricDevice(
#     device_ip=DEVICE_IP,
#     port=DEVICE_PORT,
#     timeout=TIMEOUT,
#     device_id=DEVICE_ID,
#     use_cloud=USE_CLOUD
# )
"""
    
    try:
        with open('working_device_config.py', 'w') as f:
            f.write(config_content)
        print(f"\n📄 Configuration saved to: working_device_config.py")
    except Exception as e:
        print(f"❌ Failed to save config: {e}")

def update_existing_scripts():
    """Suggest updates to existing scripts"""
    print(f"\n📝 TO UPDATE YOUR EXISTING SCRIPTS:")
    print("-" * 40)
    print("Change the port from 32150 to 80 in these files:")
    print("   - simple_test_181.py")
    print("   - connect_device_181_advanced.py")
    print("   - configure_device_181.py")
    print()
    print("Example change:")
    print("   OLD: port=32150")
    print("   NEW: port=80")

def main():
    """Main test function"""
    print("🔍 ZK DEVICE PORT 80 CONNECTION TEST")
    print("=" * 60)
    
    # Test configurations on port 80
    working_config = test_port_80_configurations()
    
    if working_config:
        print(f"\n✅ SUCCESS! Device is working on port 80")
        
        # Test different timeouts
        best_timeout = test_port_80_with_different_timeouts()
        if best_timeout:
            print(f"   Best timeout: {best_timeout}s")
        
        # Create config file
        create_working_config_file(working_config)
        
        # Provide update instructions
        update_existing_scripts()
        
        print(f"\n🎯 NEXT STEPS:")
        print("1. Use port 80 instead of 32150 in your applications")
        print("2. Update your existing test scripts")
        print("3. Test the connection with your main application")
        
    else:
        print(f"\n❌ No working configuration found on port 80")
        print("Device may need different approach or factory reset")

if __name__ == '__main__':
    main()
