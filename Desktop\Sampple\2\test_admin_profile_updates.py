#!/usr/bin/env python3
"""
Test the updated admin staff profile with on-duty and permission applications
"""

import sqlite3
import datetime
import json

def test_comprehensive_staff_profile_route():
    """Test the updated get_comprehensive_staff_profile route"""
    print("=== Testing Updated get_comprehensive_staff_profile Route ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a staff member with applications
        cursor.execute('SELECT id, full_name FROM staff LIMIT 1')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff members found")
            return False
            
        staff_id = staff['id']
        print(f"Testing with staff: {staff['full_name']} (ID: {staff_id})")
        
        # Simulate the route logic
        thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).date()
        
        # Get staff information
        staff_info = cursor.execute('''
            SELECT s.*, sc.name as school_name
            FROM staff s
            LEFT JOIN schools sc ON s.school_id = sc.id
            WHERE s.id = ?
        ''', (staff_id,)).fetchone()
        
        # Get attendance records
        attendance = cursor.execute('''
            SELECT date, time_in, time_out, overtime_in, overtime_out, status
            FROM attendance
            WHERE staff_id = ? AND date >= ?
            ORDER BY date DESC
        ''', (staff_id, thirty_days_ago)).fetchall()
        
        # Get biometric verifications
        verifications = cursor.execute('''
            SELECT verification_type, verification_time, verification_status, device_ip
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) >= ?
            ORDER BY verification_time DESC
            LIMIT 50
        ''', (staff_id, thirty_days_ago)).fetchall()
        
        # Get leave applications
        leaves = cursor.execute('''
            SELECT leave_type, start_date, end_date, reason, status, applied_at
            FROM leave_applications
            WHERE staff_id = ?
            ORDER BY applied_at DESC
            LIMIT 20
        ''', (staff_id,)).fetchall()
        
        # Get on-duty applications
        on_duty_applications = cursor.execute('''
            SELECT duty_type, start_date, end_date, start_time, end_time, location, purpose, reason, status, applied_at, admin_remarks
            FROM on_duty_applications
            WHERE staff_id = ?
            ORDER BY applied_at DESC
            LIMIT 20
        ''', (staff_id,)).fetchall()
        
        # Get permission applications
        permission_applications = cursor.execute('''
            SELECT permission_type, permission_date, start_time, end_time, duration_hours, reason, status, applied_at, admin_remarks
            FROM permission_applications
            WHERE staff_id = ?
            ORDER BY applied_at DESC
            LIMIT 20
        ''', (staff_id,)).fetchall()
        
        # Calculate attendance statistics
        total_days = len(attendance)
        present_days = len([a for a in attendance if a['status'] in ['present', 'late']])
        absent_days = len([a for a in attendance if a['status'] == 'absent'])
        late_days = len([a for a in attendance if a['status'] == 'late'])
        
        attendance_stats = {
            'total_days': total_days,
            'present_days': present_days,
            'absent_days': absent_days,
            'late_days': late_days,
            'attendance_rate': round((present_days / total_days * 100) if total_days > 0 else 0, 1)
        }
        
        # Simulate the response
        response_data = {
            'success': True,
            'staff': dict(staff_info) if staff_info else None,
            'attendance': [dict(a) for a in attendance],
            'verifications': [dict(v) for v in verifications],
            'leaves': [dict(l) for l in leaves],
            'on_duty_applications': [dict(od) for od in on_duty_applications],
            'permission_applications': [dict(p) for p in permission_applications],
            'attendance_stats': attendance_stats
        }
        
        print(f"✅ Staff info: {'Found' if staff_info else 'Not found'}")
        print(f"✅ Attendance records: {len(response_data['attendance'])}")
        print(f"✅ Biometric verifications: {len(response_data['verifications'])}")
        print(f"✅ Leave applications: {len(response_data['leaves'])}")
        print(f"✅ On-duty applications: {len(response_data['on_duty_applications'])}")
        print(f"✅ Permission applications: {len(response_data['permission_applications'])}")
        print(f"✅ Attendance stats: {response_data['attendance_stats']}")
        
        # Show sample data
        if response_data['on_duty_applications']:
            print("\nSample on-duty applications:")
            for i, duty in enumerate(response_data['on_duty_applications'][:3]):
                print(f"  {i+1}. {duty['duty_type']}: {duty['purpose'][:50]}... ({duty['status']})")
        
        if response_data['permission_applications']:
            print("\nSample permission applications:")
            for i, permission in enumerate(response_data['permission_applications'][:3]):
                print(f"  {i+1}. {permission['permission_type']}: {permission['duration_hours']}h on {permission['permission_date']} ({permission['status']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing route: {e}")
        return False
    finally:
        conn.close()

def test_javascript_tab_structure():
    """Test if the JavaScript has the correct tab structure"""
    print("\n=== Testing JavaScript Tab Structure ===")
    
    try:
        with open('static/js/admin_dashboard.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for new tabs
        required_elements = [
            'on-duty-tab',
            'permissions-tab',
            'on-duty-pane',
            'permissions-pane',
            'on_duty_applications',
            'permission_applications',
            'duty_type',
            'permission_type',
            'duration_hours'
        ]
        
        all_found = True
        for element in required_elements:
            if element in content:
                print(f"✅ Found: {element}")
            else:
                print(f"❌ Missing: {element}")
                all_found = False
        
        return all_found
        
    except FileNotFoundError:
        print("❌ admin_dashboard.js not found")
        return False
    except Exception as e:
        print(f"❌ Error checking JavaScript: {e}")
        return False

def test_data_display_formatting():
    """Test the data display formatting"""
    print("\n=== Testing Data Display Formatting ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get sample data
        cursor.execute('SELECT * FROM on_duty_applications LIMIT 1')
        sample_duty = cursor.fetchone()
        
        cursor.execute('SELECT * FROM permission_applications LIMIT 1')
        sample_permission = cursor.fetchone()
        
        if sample_duty:
            print("✅ Sample on-duty application data:")
            print(f"  - Duty Type: {sample_duty['duty_type']}")
            print(f"  - Date Range: {sample_duty['start_date']} to {sample_duty['end_date']}")
            print(f"  - Time: {sample_duty['start_time'] or 'All day'} - {sample_duty['end_time'] or 'All day'}")
            print(f"  - Location: {sample_duty['location'] or 'Not specified'}")
            print(f"  - Purpose: {sample_duty['purpose'][:50]}...")
            print(f"  - Status: {sample_duty['status']}")
        else:
            print("⚠️  No on-duty applications found")
        
        if sample_permission:
            print("\n✅ Sample permission application data:")
            print(f"  - Permission Type: {sample_permission['permission_type']}")
            print(f"  - Date: {sample_permission['permission_date']}")
            print(f"  - Time: {sample_permission['start_time']} - {sample_permission['end_time']}")
            print(f"  - Duration: {sample_permission['duration_hours']} hours")
            print(f"  - Reason: {sample_permission['reason'][:50]}...")
            print(f"  - Status: {sample_permission['status']}")
        else:
            print("⚠️  No permission applications found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing data formatting: {e}")
        return False
    finally:
        conn.close()

def create_additional_test_data():
    """Create additional test data for better testing"""
    print("\n=== Creating Additional Test Data ===")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Get a staff member
        cursor.execute('SELECT id FROM staff LIMIT 1')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff members found")
            return False
        
        staff_id = staff[0]
        
        # Create additional on-duty applications with different statuses
        test_duties = [
            ('Meeting', '2025-07-22', '2025-07-22', '10:00', '12:00', 'Conference Room A', 'Department meeting', 'Monthly review meeting', 'approved'),
            ('Field Work', '2025-07-25', '2025-07-26', None, None, 'Client Site', 'Site inspection and maintenance', 'Routine maintenance work', 'pending'),
            ('Training', '2025-07-30', '2025-07-30', '09:00', '17:00', 'Training Center', 'Professional development workshop', 'Skills enhancement', 'rejected')
        ]
        
        for duty in test_duties:
            cursor.execute('''
                INSERT OR IGNORE INTO on_duty_applications 
                (staff_id, school_id, duty_type, start_date, end_date, start_time, end_time, location, purpose, reason, status)
                VALUES (?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (staff_id,) + duty)
        
        # Create additional permission applications with different statuses
        test_permissions = [
            ('Medical', '2025-07-19', '10:00', '12:00', 2.0, 'Medical checkup appointment', 'approved'),
            ('Personal Work', '2025-07-21', '14:00', '15:30', 1.5, 'Bank work and documentation', 'pending'),
            ('Emergency', '2025-07-23', '11:00', '13:00', 2.0, 'Family emergency situation', 'rejected')
        ]
        
        for permission in test_permissions:
            cursor.execute('''
                INSERT OR IGNORE INTO permission_applications 
                (staff_id, school_id, permission_type, permission_date, start_time, end_time, duration_hours, reason, status)
                VALUES (?, 1, ?, ?, ?, ?, ?, ?, ?)
            ''', (staff_id,) + permission)
        
        conn.commit()
        
        # Verify the data was created
        cursor.execute('SELECT COUNT(*) FROM on_duty_applications WHERE staff_id = ?', (staff_id,))
        duty_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM permission_applications WHERE staff_id = ?', (staff_id,))
        permission_count = cursor.fetchone()[0]
        
        print(f"✅ Created test data:")
        print(f"  - On-duty applications: {duty_count} total")
        print(f"  - Permission applications: {permission_count} total")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🧪 Testing Admin Staff Profile Updates")
    print("=" * 60)
    
    results = []
    
    # Create additional test data first
    create_additional_test_data()
    
    # Run all tests
    results.append(("Comprehensive Staff Profile Route", test_comprehensive_staff_profile_route()))
    results.append(("JavaScript Tab Structure", test_javascript_tab_structure()))
    results.append(("Data Display Formatting", test_data_display_formatting()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\nAdmin staff profile now includes:")
        print("1. ✅ Leave Applications tab (existing)")
        print("2. ✅ On Duty Applications tab (new)")
        print("3. ✅ Permission Applications tab (new)")
        print("4. ✅ Complete application history with status")
        print("5. ✅ Proper data formatting and display")
        
        print("\n🚀 Ready to test in the application!")
        print("Login as admin and view any staff profile to see the new tabs.")
    else:
        print(f"\n⚠️  {total - passed} issues need attention")
        print("Please check the failed tests above.")
