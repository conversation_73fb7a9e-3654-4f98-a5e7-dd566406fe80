#!/usr/bin/env python3
"""
Test to verify that biometric attendance functionality has been removed from staff dashboard
"""

import os
import re

def test_html_biometric_removal():
    """Test that biometric sections are removed from HTML"""
    print("=== Testing HTML Biometric Removal ===")
    
    html_file = 'templates/staff_dashboard.html'
    
    if not os.path.exists(html_file):
        print("❌ Staff dashboard HTML file not found")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for biometric-related content that should be removed
    biometric_patterns = [
        r'Biometric Attendance',
        r'How to Mark Attendance',
        r'Go to the biometric device',
        r'Place your finger on the scanner',
        r'Device Status',
        r'deviceStatus',
        r'authStatus',
        r'attendanceUpdateStatus',
        r'biometric device',
        r'verification type',
        r'Check-in.*Start of regular work hours',
        r'Overtime-in.*Start of overtime hours'
    ]
    
    found_patterns = []
    for pattern in biometric_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            found_patterns.append(pattern)
    
    if found_patterns:
        print("❌ Found biometric-related content in HTML:")
        for pattern in found_patterns:
            print(f"  - {pattern}")
        return False
    else:
        print("✅ No biometric-related content found in HTML")
        return True

def test_javascript_biometric_removal():
    """Test that biometric functions are removed from JavaScript"""
    print("\n=== Testing JavaScript Biometric Removal ===")
    
    js_file = 'static/js/staff_dashboard.js'
    
    if not os.path.exists(js_file):
        print("❌ Staff dashboard JavaScript file not found")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for biometric-related functions that should be removed
    biometric_functions = [
        r'function fingerprintScan',
        r'function faceRecognition',
        r'function completeAuthentication',
        r'function startDevicePolling',
        r'function pollForDeviceVerifications',
        r'function showVerificationSuccess',
        r'function checkDeviceStatus',
        r'function updateAvailableActions',
        r'startAuthBtn',
        r'fingerprintScanner',
        r'faceScanner',
        r'authStatus',
        r'getElementById\(\'attendanceStatus\'\)',  # More specific pattern
        r'deviceStatus',
        r'biometric',
        r'test_biometric_connection',
        r'get_latest_device_verifications'
    ]
    
    found_functions = []
    for func in biometric_functions:
        if re.search(func, content, re.IGNORECASE):
            found_functions.append(func)
    
    if found_functions:
        print("❌ Found biometric-related functions in JavaScript:")
        for func in found_functions:
            print(f"  - {func}")
        return False
    else:
        print("✅ No biometric-related functions found in JavaScript")
        return True

def test_table_structure():
    """Test that verification history table structure is updated"""
    print("\n=== Testing Table Structure ===")
    
    html_file = 'templates/staff_dashboard.html'
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that table has correct number of columns (3 instead of 4)
    table_header_pattern = r'<th>Time</th>\s*<th>Type</th>\s*<th>Status</th>'
    if re.search(table_header_pattern, content):
        print("✅ Table header updated correctly (3 columns)")
        
        # Check that colspan is updated to 3
        colspan_pattern = r'colspan="3"'
        if re.search(colspan_pattern, content):
            print("✅ Table colspan updated correctly")
            return True
        else:
            print("❌ Table colspan not updated")
            return False
    else:
        print("❌ Table header not updated correctly")
        return False

def test_javascript_table_rendering():
    """Test that JavaScript table rendering is updated"""
    print("\n=== Testing JavaScript Table Rendering ===")
    
    js_file = 'static/js/staff_dashboard.js'
    
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that table row rendering has 3 columns instead of 4
    # Should not have biometric_method column
    if 'biometric_method' in content:
        print("❌ Found biometric_method reference in JavaScript")
        return False
    
    # Check for correct table row structure
    table_row_pattern = r'<td>\$\{time\}</td>\s*<td>\$\{v\.verification_type\}</td>\s*<td>\$\{statusBadge\}</td>'
    if re.search(table_row_pattern, content):
        print("✅ JavaScript table rendering updated correctly (3 columns)")
        return True
    else:
        print("❌ JavaScript table rendering not updated correctly")
        return False

def test_remaining_functionality():
    """Test that essential functionality remains intact"""
    print("\n=== Testing Remaining Functionality ===")
    
    html_file = 'templates/staff_dashboard.html'
    js_file = 'static/js/staff_dashboard.js'
    
    # Check that essential elements remain
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    with open(js_file, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    essential_elements = [
        ('HTML', html_content, [
            r'Today\'s Verification History',
            r'verificationHistory',
            r'Monthly Calendar',
            r'Apply Leave',
            r'Apply On Duty'
        ]),
        ('JavaScript', js_content, [
            r'loadTodayAttendanceStatus',
            r'submitLeave',
            r'submitOnDuty',
            r'submitPermission'
        ])
    ]
    
    all_good = True
    for file_type, content, patterns in essential_elements:
        print(f"\n{file_type} essential elements:")
        for pattern in patterns:
            if re.search(pattern, content, re.IGNORECASE):
                print(f"  ✅ {pattern}")
            else:
                print(f"  ❌ {pattern}")
                all_good = False
    
    return all_good

if __name__ == "__main__":
    print("🧹 Testing Biometric Attendance Removal")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("HTML Biometric Removal", test_html_biometric_removal()))
    results.append(("JavaScript Biometric Removal", test_javascript_biometric_removal()))
    results.append(("Table Structure Update", test_table_structure()))
    results.append(("JavaScript Table Rendering", test_javascript_table_rendering()))
    results.append(("Remaining Functionality", test_remaining_functionality()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 BIOMETRIC ATTENDANCE SUCCESSFULLY REMOVED!")
        print("\nWhat was removed:")
        print("1. ✅ Biometric Attendance section from staff dashboard")
        print("2. ✅ Device status display and instructions")
        print("3. ✅ Biometric authentication functions from JavaScript")
        print("4. ✅ Device polling and connection checking")
        print("5. ✅ Biometric method column from verification table")
        print("6. ✅ All biometric-related UI elements and handlers")
        
        print("\nWhat remains intact:")
        print("1. ✅ Today's Verification History table (simplified)")
        print("2. ✅ Leave application functionality")
        print("3. ✅ On-duty application functionality")
        print("4. ✅ Permission application functionality")
        print("5. ✅ Monthly calendar navigation")
        print("6. ✅ Dashboard statistics and charts")
        
        print("\n🚀 Staff dashboard is clean and ready!")
    else:
        print(f"\n⚠️  {total - passed} issues need attention")
        print("Please check the failed tests above.")
