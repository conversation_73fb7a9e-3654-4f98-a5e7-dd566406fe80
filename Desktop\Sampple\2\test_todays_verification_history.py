#!/usr/bin/env python3
"""
Today's Verification History Test Suite

This script tests the Today's Verification History functionality to identify
and fix any issues with the feature.
"""

import sqlite3
import os
import sys
import requests
import json
from datetime import datetime, date, timedelta


class TodaysVerificationHistoryTestSuite:
    """Test suite for Today's Verification History functionality"""
    
    def __init__(self):
        self.test_results = []
        self.base_url = "http://127.0.0.1:5000"
        self.session = requests.Session()
    
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_database_today_verifications(self):
        """Test database query for today's verifications"""
        print("\n=== Testing Database Today's Verifications ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            today = date.today()
            
            # Test the exact query used in the endpoint
            cursor.execute('''
                SELECT verification_type, verification_time, biometric_method, verification_status
                FROM biometric_verifications
                WHERE staff_id = ? AND DATE(verification_time) = ?
                ORDER BY verification_time DESC
            ''', (20, today))  # Using staff_id 20 from our previous tests
            
            results = cursor.fetchall()
            
            if results:
                self.log_test("Database - Today's verifications query works", True, f"Found {len(results)} records for today")
                
                # Check data structure
                first_result = results[0]
                if len(first_result) == 4:
                    self.log_test("Database - Query returns correct fields", True)
                else:
                    self.log_test("Database - Query returns correct fields", False, f"Expected 4 fields, got {len(first_result)}")
                
                # Check for today's data specifically
                for result in results:
                    verification_time = result[1]
                    if verification_time:
                        # Parse the datetime string
                        try:
                            dt = datetime.fromisoformat(verification_time.replace('Z', '+00:00'))
                            if dt.date() == today:
                                self.log_test("Database - Contains today's verification data", True)
                                break
                        except:
                            pass
                else:
                    self.log_test("Database - Contains today's verification data", False, "No today's data found")
            else:
                self.log_test("Database - Today's verifications query works", False, "No results returned")
            
            # Test with different date ranges
            yesterday = today - timedelta(days=1)
            cursor.execute('''
                SELECT COUNT(*) FROM biometric_verifications
                WHERE staff_id = ? AND DATE(verification_time) = ?
            ''', (20, yesterday))
            
            yesterday_count = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT COUNT(*) FROM biometric_verifications
                WHERE staff_id = ? AND DATE(verification_time) = ?
            ''', (20, today))
            
            today_count = cursor.fetchone()[0]
            
            self.log_test("Database - Date filtering works", True, f"Yesterday: {yesterday_count}, Today: {today_count}")
            
            conn.close()
            
        except Exception as e:
            self.log_test("Database today's verifications test", False, str(e))
    
    def test_api_endpoint(self):
        """Test the /get_today_attendance_status API endpoint"""
        print("\n=== Testing API Endpoint ===")
        
        try:
            # Test endpoint accessibility
            response = self.session.get(f"{self.base_url}/get_today_attendance_status")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success') == False and 'Unauthorized' in data.get('error', ''):
                        self.log_test("API - Endpoint accessible (requires auth)", True, "Returns proper auth error")
                    elif data.get('success') == True:
                        self.log_test("API - Endpoint returns data", True)
                        
                        # Check response structure
                        required_keys = ['attendance', 'verifications', 'available_actions']
                        missing_keys = [key for key in required_keys if key not in data]
                        
                        if not missing_keys:
                            self.log_test("API - Response has correct structure", True)
                            
                            # Check verifications structure
                            verifications = data.get('verifications', [])
                            if isinstance(verifications, list):
                                self.log_test("API - Verifications is a list", True, f"Contains {len(verifications)} items")
                                
                                if verifications:
                                    first_verification = verifications[0]
                                    expected_fields = ['verification_type', 'verification_time', 'biometric_method', 'verification_status']
                                    missing_fields = [field for field in expected_fields if field not in first_verification]
                                    
                                    if not missing_fields:
                                        self.log_test("API - Verification objects have correct fields", True)
                                    else:
                                        self.log_test("API - Verification objects have correct fields", False, f"Missing: {missing_fields}")
                            else:
                                self.log_test("API - Verifications is a list", False, f"Got {type(verifications)}")
                        else:
                            self.log_test("API - Response has correct structure", False, f"Missing keys: {missing_keys}")
                    else:
                        self.log_test("API - Endpoint returns valid response", False, f"Unexpected response: {data}")
                except json.JSONDecodeError:
                    self.log_test("API - Endpoint returns JSON", False, "Invalid JSON response")
            else:
                self.log_test("API - Endpoint accessible", False, f"Status code: {response.status_code}")
                
        except Exception as e:
            self.log_test("API endpoint test", False, str(e))
    
    def test_javascript_function(self):
        """Test JavaScript function logic"""
        print("\n=== Testing JavaScript Function Logic ===")
        
        # Check if the JavaScript file exists
        js_file = 'static/js/staff_dashboard.js'
        if os.path.exists(js_file):
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for required functions
                if 'updateVerificationHistory' in content:
                    self.log_test("JavaScript - updateVerificationHistory function exists", True)
                else:
                    self.log_test("JavaScript - updateVerificationHistory function exists", False)
                
                if 'loadTodayAttendanceStatus' in content:
                    self.log_test("JavaScript - loadTodayAttendanceStatus function exists", True)
                else:
                    self.log_test("JavaScript - loadTodayAttendanceStatus function exists", False)
                
                # Check for proper API call
                if '/get_today_attendance_status' in content:
                    self.log_test("JavaScript - Makes correct API call", True)
                else:
                    self.log_test("JavaScript - Makes correct API call", False)
                
                # Check for DOM element targeting
                if 'verificationHistory' in content:
                    self.log_test("JavaScript - Targets correct DOM element", True)
                else:
                    self.log_test("JavaScript - Targets correct DOM element", False)
                
                # Check for error handling
                if 'catch(error' in content:
                    self.log_test("JavaScript - Has error handling", True)
                else:
                    self.log_test("JavaScript - Has error handling", False)
                
            except Exception as e:
                self.log_test("JavaScript file check", False, str(e))
        else:
            self.log_test("JavaScript - File exists", False, "staff_dashboard.js not found")
    
    def test_html_template(self):
        """Test HTML template structure"""
        print("\n=== Testing HTML Template ===")
        
        template_file = 'templates/staff_dashboard.html'
        if os.path.exists(template_file):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for verification history section
                if "Today's Verification History" in content:
                    self.log_test("HTML - Has verification history section", True)
                else:
                    self.log_test("HTML - Has verification history section", False)
                
                # Check for correct table structure
                if 'id="verificationHistory"' in content:
                    self.log_test("HTML - Has correct table ID", True)
                else:
                    self.log_test("HTML - Has correct table ID", False)
                
                # Check for table headers
                required_headers = ['Time', 'Type', 'Method', 'Status']
                missing_headers = [header for header in required_headers if header not in content]
                
                if not missing_headers:
                    self.log_test("HTML - Has correct table headers", True)
                else:
                    self.log_test("HTML - Has correct table headers", False, f"Missing: {missing_headers}")
                
                # Check for default message
                if 'No verifications today' in content:
                    self.log_test("HTML - Has default empty message", True)
                else:
                    self.log_test("HTML - Has default empty message", False)
                
            except Exception as e:
                self.log_test("HTML template check", False, str(e))
        else:
            self.log_test("HTML - Template exists", False, "staff_dashboard.html not found")
    
    def test_data_formatting(self):
        """Test data formatting and display logic"""
        print("\n=== Testing Data Formatting ===")
        
        try:
            conn = sqlite3.connect('vishnorex.db')
            cursor = conn.cursor()
            
            # Get a sample verification record
            cursor.execute('''
                SELECT verification_type, verification_time, biometric_method, verification_status
                FROM biometric_verifications
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            
            if result:
                verification_type, verification_time, biometric_method, verification_status = result
                
                # Test time formatting
                try:
                    if verification_time:
                        dt = datetime.fromisoformat(verification_time.replace('Z', '+00:00'))
                        formatted_time = dt.strftime('%I:%M:%S %p')
                        self.log_test("Data - Time formatting works", True, f"Sample: {formatted_time}")
                    else:
                        self.log_test("Data - Time formatting works", False, "No verification_time found")
                except Exception as e:
                    self.log_test("Data - Time formatting works", False, f"Formatting error: {e}")
                
                # Test verification type display
                if verification_type:
                    display_type = verification_type.replace('-', ' ').title()
                    self.log_test("Data - Verification type formatting", True, f"'{verification_type}' -> '{display_type}'")
                else:
                    self.log_test("Data - Verification type formatting", False, "No verification_type found")
                
                # Test status badge logic
                if verification_status:
                    badge_class = 'bg-success' if verification_status == 'success' else 'bg-danger'
                    self.log_test("Data - Status badge logic", True, f"'{verification_status}' -> '{badge_class}'")
                else:
                    self.log_test("Data - Status badge logic", False, "No verification_status found")
            else:
                self.log_test("Data formatting test", False, "No sample data available")
            
            conn.close()
            
        except Exception as e:
            self.log_test("Data formatting test", False, str(e))
    
    def run_all_tests(self):
        """Run all Today's Verification History tests"""
        print("🔍 Starting Today's Verification History Test Suite")
        print("=" * 60)
        
        self.test_database_today_verifications()
        self.test_api_endpoint()
        self.test_javascript_function()
        self.test_html_template()
        self.test_data_formatting()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TODAY'S VERIFICATION HISTORY TEST SUMMARY")
        print("=" * 60)
        
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['passed']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['message']}")
        
        return passed_tests == total_tests


def main():
    """Main function"""
    print("🔍 VishnoRex Today's Verification History Test Suite")
    print("=" * 60)
    print("Testing Today's Verification History functionality to identify issues")
    print()
    
    test_suite = TodaysVerificationHistoryTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 All Today's Verification History tests passed!")
        print("✅ The functionality is working correctly")
    else:
        print("\n⚠️  Some tests failed - issues identified")
        print("📝 Review the failed tests to understand the problems")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
