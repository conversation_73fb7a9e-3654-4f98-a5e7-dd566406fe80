#!/usr/bin/env python3
"""
Debug the attendance loading issue by simulating the exact request
"""

import sys
import os
sys.path.append('.')

from app import app, get_db
import datetime
import json

def test_route_with_session():
    """Test the route with a proper session"""
    print("=== Testing Route with Session ===")
    
    with app.test_client() as client:
        # First, let's check if we can create a session
        with client.session_transaction() as sess:
            sess['user_id'] = 40
            sess['user_type'] = 'staff'
            sess['full_name'] = 'Test Staff'
            sess['school_id'] = 1
        
        # Make the request
        response = client.get('/get_today_attendance_status')
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.content_type}")
        
        if response.status_code == 200:
            try:
                data = response.get_json()
                print(f"Response JSON: {json.dumps(data, indent=2)}")
                
                if data and data.get('success'):
                    print("✅ Route is working correctly")
                    print(f"Attendance: {data.get('attendance')}")
                    print(f"Verifications: {len(data.get('verifications', []))} records")
                    print(f"Available Actions: {data.get('available_actions')}")
                    return True
                else:
                    print(f"❌ Route returned error: {data.get('error') if data else 'No data'}")
                    return False
            except Exception as e:
                print(f"❌ JSON parsing error: {e}")
                print(f"Raw response: {response.get_data(as_text=True)}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.get_data(as_text=True)}")
            return False

def test_format_functions():
    """Test the formatting functions"""
    print("\n=== Testing Format Functions ===")
    
    try:
        from app import format_time_to_12hr, format_attendance_times_to_12hr
        
        # Test format_time_to_12hr
        test_times = ['09:30:00', '14:45:30', '23:59:59', None, '']
        
        print("Testing format_time_to_12hr:")
        for time_str in test_times:
            result = format_time_to_12hr(time_str)
            print(f"  {time_str} -> {result}")
        
        # Test format_attendance_times_to_12hr
        test_attendance = {
            'time_in': '09:30:00',
            'time_out': '17:30:00',
            'overtime_in': None,
            'overtime_out': None,
            'status': 'present'
        }
        
        print("\nTesting format_attendance_times_to_12hr:")
        result = format_attendance_times_to_12hr(test_attendance)
        print(f"  Input: {test_attendance}")
        print(f"  Output: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Format function error: {e}")
        return False

def test_database_connection():
    """Test database connection and queries"""
    print("\n=== Testing Database Connection ===")
    
    try:
        db = get_db()
        
        # Test staff query
        staff = db.execute('SELECT id, staff_id, full_name FROM staff WHERE id = 40').fetchone()
        if staff:
            print(f"✅ Staff found: {staff['full_name']} (ID: {staff['id']})")
        else:
            print("❌ Staff not found")
            return False
        
        # Test attendance query
        today = datetime.date.today()
        attendance = db.execute('''
            SELECT time_in, time_out, overtime_in, overtime_out, status
            FROM attendance
            WHERE staff_id = ? AND date = ?
        ''', (40, today)).fetchone()
        
        print(f"✅ Attendance query executed")
        print(f"  Today's attendance: {dict(attendance) if attendance else 'None'}")
        
        # Test verifications query
        verifications = db.execute('''
            SELECT verification_type, verification_time, verification_status, biometric_method
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) = ?
            ORDER BY verification_time DESC
        ''', (40, today)).fetchall()
        
        print(f"✅ Verifications query executed")
        print(f"  Today's verifications: {len(verifications)} records")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def check_javascript_file():
    """Check if JavaScript file has any obvious issues"""
    print("\n=== Checking JavaScript File ===")
    
    js_file = 'static/js/staff_dashboard.js'
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for common issues
        issues = []
        
        if 'updateAvailableActions(' in content:
            issues.append("Still references removed updateAvailableActions function")
        
        if 'function loadTodayAttendanceStatus()' not in content:
            issues.append("loadTodayAttendanceStatus function not found")
        
        if 'showErrorMessage' not in content:
            issues.append("showErrorMessage function not found")
        
        if '/get_today_attendance_status' not in content:
            issues.append("Route URL not found in JavaScript")
        
        if issues:
            print("❌ JavaScript issues found:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ JavaScript file looks correct")
            return True
        
    except Exception as e:
        print(f"❌ Error reading JavaScript file: {e}")
        return False

def check_html_file():
    """Check if HTML file has error container"""
    print("\n=== Checking HTML File ===")
    
    html_file = 'templates/staff_dashboard.html'
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'id="errorContainer"' in content:
            print("✅ Error container found in HTML")
        else:
            print("❌ Error container not found in HTML")
            return False
        
        if 'loadTodayAttendanceStatus()' in content:
            print("✅ Function call found in HTML")
        else:
            print("❌ Function call not found in HTML")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading HTML file: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Debugging Attendance Loading Issue")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("Database Connection", test_database_connection()))
    results.append(("Format Functions", test_format_functions()))
    results.append(("Route with Session", test_route_with_session()))
    results.append(("JavaScript File", check_javascript_file()))
    results.append(("HTML File", check_html_file()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEBUG RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL BACKEND COMPONENTS ARE WORKING!")
        print("\nIf you're still seeing 'Failed to load attendance data':")
        print("1. 🌐 Check browser console for JavaScript errors")
        print("2. 🔄 Hard refresh the page (Ctrl+F5)")
        print("3. 🧹 Clear browser cache and cookies")
        print("4. 🔍 Check Network tab in browser dev tools")
        print("5. 📱 Try in incognito/private browsing mode")
        print("\nThe issue is likely in the browser/frontend, not the backend.")
    else:
        print(f"\n⚠️  {total - passed} backend issues found")
        print("These need to be fixed before the frontend will work.")
