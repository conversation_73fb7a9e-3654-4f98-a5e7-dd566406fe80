<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Registration - ESSL Fingerprint System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 2rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: #6c757d;
        }
        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step-line {
            width: 50px;
            height: 2px;
            background: #e9ecef;
            margin-top: 19px;
        }
        .step-line.completed {
            background: #28a745;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                User Registration
                            </h2>
                            <p class="mb-0 mt-2">Register new user and enroll biometric data</p>
                        </div>
                        <div class="card-body p-4">
                            <!-- Step Indicator -->
                            <div class="step-indicator">
                                <div class="step active" id="step1">1</div>
                                <div class="step-line" id="line1"></div>
                                <div class="step" id="step2">2</div>
                            </div>

                            <!-- Step 1: User Registration Form -->
                            <div id="registrationStep">
                                <h4 class="mb-4 text-center">Step 1: User Information</h4>
                                <form id="registrationForm">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="school_id" class="form-label fw-bold">Organization</label>
                                            <select class="form-select" id="school_id" name="school_id" required>
                                                <option value="">Select organization...</option>
                                                {% for school in schools %}
                                                <option value="{{ school.id }}" 
                                                    {% if request.args.get('school_id') == school.id|string %}selected{% endif %}>
                                                    {{ school.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="staff_id" class="form-label fw-bold">Staff ID</label>
                                            <input type="text" class="form-control" id="staff_id" name="staff_id" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="full_name" class="form-label fw-bold">Full Name</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" name="email">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone</label>
                                            <input type="tel" class="form-control" id="phone" name="phone">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="department" class="form-label">Department</label>
                                            <input type="text" class="form-control" id="department" name="department">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="position" class="form-label">Position</label>
                                            <input type="text" class="form-control" id="position" name="position">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="password" class="form-label fw-bold">Password</label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            Register User
                                        </button>
                                        <a href="/" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </form>
                            </div>

                            <!-- Step 2: Biometric Enrollment -->
                            <div id="biometricStep" style="display: none;">
                                <h4 class="mb-4 text-center">Step 2: Biometric Enrollment</h4>
                                <div class="text-center">
                                    <div class="mb-4">
                                        <i class="fas fa-fingerprint" style="font-size: 4rem; color: #667eea;"></i>
                                    </div>
                                    <p class="mb-4">User registered successfully! Now enroll biometric data.</p>
                                    
                                    <div class="mb-3">
                                        <label for="device_ip" class="form-label">Device IP Address</label>
                                        <input type="text" class="form-control" id="device_ip" value="*************">
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary" onclick="enrollBiometric()">
                                            <i class="fas fa-fingerprint me-2"></i>
                                            Enroll Fingerprint
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="testVerification()">
                                            <i class="fas fa-check-circle me-2"></i>
                                            Test Verification
                                        </button>
                                        <a href="/" class="btn btn-outline-secondary">
                                            <i class="fas fa-home me-2"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    <script>
        let currentStaffDbId = null;

        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/register', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Response is not JSON');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    currentStaffDbId = data.staff_db_id;

                    // Update step indicator
                    document.getElementById('step1').classList.remove('active');
                    document.getElementById('step1').classList.add('completed');
                    document.getElementById('line1').classList.add('completed');
                    document.getElementById('step2').classList.add('active');

                    // Show biometric step
                    document.getElementById('registrationStep').style.display = 'none';
                    document.getElementById('biometricStep').style.display = 'block';

                    alert('Registration successful! Please proceed to biometric enrollment.');
                } else {
                    alert('Registration failed: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Registration error:', error);
                alert('Error: ' + error.message);
            });
        });

        function enrollBiometric() {
            if (!currentStaffDbId) {
                alert('Please complete user registration first');
                return;
            }
            
            const deviceIp = document.getElementById('device_ip').value;
            const formData = new FormData();
            formData.append('staff_db_id', currentStaffDbId);
            formData.append('device_ip', deviceIp);
            
            fetch('/enroll_biometric', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    document.getElementById('step2').classList.remove('active');
                    document.getElementById('step2').classList.add('completed');
                } else {
                    alert('Enrollment failed: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function testVerification() {
            const staffId = document.getElementById('staff_id').value;
            const deviceIp = document.getElementById('device_ip').value;
            
            if (!staffId) {
                alert('Staff ID is required for verification');
                return;
            }
            
            const formData = new FormData();
            formData.append('staff_id', staffId);
            formData.append('device_ip', deviceIp);
            
            fetch('/verify_biometric', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Verification successful: ' + data.message);
                } else {
                    alert('Verification failed: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }
    </script>
</body>
</html>
