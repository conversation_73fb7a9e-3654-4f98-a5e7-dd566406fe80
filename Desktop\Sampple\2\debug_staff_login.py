#!/usr/bin/env python3
"""
Debug staff login issues
"""

import sqlite3
from werkzeug.security import check_password_hash, generate_password_hash

def test_staff_login(staff_id, password, school_id=4):
    """Test staff login with detailed debugging"""
    
    print(f"=== Testing Login for Staff ID: {staff_id} ===")
    print(f"School ID: {school_id}")
    print(f"Password: {password}")
    print()
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Step 1: Check if staff exists
        print("Step 1: Checking if staff exists...")
        cursor.execute('''
            SELECT * FROM staff 
            WHERE school_id = ? AND staff_id = ?
        ''', (school_id, staff_id))
        staff = cursor.fetchone()
        
        if not staff:
            print(f"❌ FAILED: Staff with ID '{staff_id}' not found in school {school_id}")
            return False
        
        print(f"✅ Staff found: {staff['full_name']}")
        print(f"   - Database ID: {staff['id']}")
        print(f"   - Staff ID: {staff['staff_id']}")
        print(f"   - School ID: {staff['school_id']}")
        print(f"   - Email: {staff['email']}")
        print()
        
        # Step 2: Check password hash
        print("Step 2: Checking password hash...")
        password_hash = staff['password_hash'] if staff['password_hash'] is not None else ''
        
        if not password_hash:
            print("❌ FAILED: No password hash found for this staff member")
            print("   Solution: Set a password for this staff member")
            return False
        
        print(f"✅ Password hash exists: {password_hash[:20]}...")
        print()
        
        # Step 3: Verify password
        print("Step 3: Verifying password...")
        if check_password_hash(password_hash, password):
            print(f"✅ SUCCESS: Password verification passed")
            print(f"   Staff '{staff['full_name']}' can login successfully")
            return True
        else:
            print(f"❌ FAILED: Password verification failed")
            print(f"   The password '{password}' does not match the stored hash")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False
    finally:
        conn.close()

def reset_staff_password(staff_id, new_password='password123', school_id=4):
    """Reset password for a specific staff member"""
    
    print(f"=== Resetting Password for Staff ID: {staff_id} ===")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Check if staff exists
        cursor.execute('SELECT id, full_name FROM staff WHERE staff_id = ? AND school_id = ?', (staff_id, school_id))
        staff = cursor.fetchone()
        
        if not staff:
            print(f"❌ Staff with ID '{staff_id}' not found")
            return False
        
        print(f"Found staff: {staff[1]} (DB ID: {staff[0]})")
        
        # Generate new password hash
        password_hash = generate_password_hash(new_password)
        
        # Update password
        cursor.execute('''
            UPDATE staff SET password_hash = ? WHERE id = ?
        ''', (password_hash, staff[0]))
        
        conn.commit()
        
        print(f"✅ Password reset successfully")
        print(f"   New password: {new_password}")
        print(f"   Staff can now login with Staff ID: {staff_id} and Password: {new_password}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting password: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def test_all_staff():
    """Test login for all staff members"""
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT staff_id, full_name, school_id FROM staff ORDER BY id')
        all_staff = cursor.fetchall()
        
        print("=== Testing All Staff Login ===")
        print(f"Found {len(all_staff)} staff members:")
        print()
        
        for staff in all_staff:
            print(f"Testing: {staff['full_name']} (Staff ID: {staff['staff_id']})")
            success = test_staff_login(staff['staff_id'], 'password123', staff['school_id'])
            print("-" * 50)
            print()
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    print("=== Staff Login Debug Tool ===\n")
    
    # Test all staff first
    test_all_staff()
    
    print("\n" + "="*60)
    print("SOLUTIONS:")
    print("1. If password verification fails, reset the password")
    print("2. If no password hash found, set a default password")
    print("3. Make sure you're using the correct Staff ID (not database ID)")
    print("="*60)
    
    # Ask if user wants to reset passwords
    try:
        reset_choice = input("\nDo you want to reset passwords for staff without passwords? (y/N): ").strip().lower()
        if reset_choice == 'y':
            # Reset password for staff 333 (Navanee)
            reset_staff_password('333', 'password123')
            print("\nTesting login after password reset:")
            test_staff_login('333', 'password123')
    except KeyboardInterrupt:
        print("\nCancelled by user")
