#!/usr/bin/env python3
"""
Count script tags in the HTML file to find the mismatch
"""

import re

def count_script_tags():
    """Count and analyze script tags"""
    
    with open('templates/staff_monthly_calendar.html', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    print("=== Script Tag Analysis ===")
    
    # Find all script-related tags
    opening_scripts = []
    closing_scripts = []
    
    for i, line in enumerate(lines, 1):
        # Find opening script tags
        if '<script' in line:
            opening_scripts.append((i, line.strip()))
        
        # Find closing script tags
        if '</script>' in line:
            closing_scripts.append((i, line.strip()))
    
    print(f"Opening script tags found: {len(opening_scripts)}")
    for line_num, line_content in opening_scripts:
        print(f"  Line {line_num}: {line_content}")
    
    print(f"\nClosing script tags found: {len(closing_scripts)}")
    for line_num, line_content in closing_scripts:
        print(f"  Line {line_num}: {line_content}")
    
    # Check for self-closing script tags
    self_closing = []
    for line_num, line_content in opening_scripts:
        if line_content.endswith('></script>'):
            self_closing.append((line_num, line_content))
    
    print(f"\nSelf-closing script tags: {len(self_closing)}")
    for line_num, line_content in self_closing:
        print(f"  Line {line_num}: {line_content}")
    
    # Calculate balance
    non_self_closing_opening = len(opening_scripts) - len(self_closing)
    total_closing = len(closing_scripts)
    
    print(f"\nBalance Analysis:")
    print(f"  Non-self-closing opening tags: {non_self_closing_opening}")
    print(f"  Total closing tags: {total_closing}")
    print(f"  Balance: {'✅ BALANCED' if non_self_closing_opening == total_closing else '❌ UNBALANCED'}")
    
    # Check for specific patterns
    print(f"\nPattern Analysis:")
    
    # Check for script tags that should be self-closing but aren't
    for line_num, line_content in opening_scripts:
        if 'src=' in line_content and not line_content.endswith('></script>'):
            print(f"  ⚠️  Line {line_num}: External script not self-closing: {line_content}")
    
    return non_self_closing_opening == total_closing

if __name__ == "__main__":
    is_balanced = count_script_tags()
    
    if is_balanced:
        print("\n✅ Script tags are properly balanced!")
    else:
        print("\n❌ Script tags are unbalanced - this needs to be fixed!")
