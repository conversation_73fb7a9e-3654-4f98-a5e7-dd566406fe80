#!/usr/bin/env python3
"""
Database Cleanup Script

Remove regularization and notification related tables and columns
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """Create a backup of the database before cleanup"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f'vishnorex_backup_{timestamp}.db'
    
    try:
        shutil.copy2('vishnorex.db', backup_name)
        print(f"✅ Database backed up to: {backup_name}")
        return True
    except Exception as e:
        print(f"❌ Failed to backup database: {e}")
        return False

def cleanup_database():
    """Remove regularization and notification components from database"""
    
    print("🧹 Starting Database Cleanup")
    print("=" * 50)
    
    # Create backup first
    if not backup_database():
        print("❌ Backup failed. Aborting cleanup.")
        return False
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # 1. Drop attendance_regularization_requests table
        print("\n📋 Removing attendance_regularization_requests table...")
        cursor.execute("DROP TABLE IF EXISTS attendance_regularization_requests")
        print("✅ attendance_regularization_requests table removed")
        
        # 2. Drop notifications table
        print("\n🔔 Removing notifications table...")
        cursor.execute("DROP TABLE IF EXISTS notifications")
        print("✅ notifications table removed")
        
        # 3. Remove regularization columns from attendance table
        print("\n📊 Removing regularization columns from attendance table...")
        
        # Get current attendance table structure
        cursor.execute("PRAGMA table_info(attendance)")
        columns = cursor.fetchall()
        
        # Filter out regularization columns
        keep_columns = []
        for col in columns:
            col_name = col[1]
            if 'regularization' not in col_name:
                keep_columns.append(f"{col_name} {col[2]}")
        
        # Create new table without regularization columns
        new_table_sql = f"""
        CREATE TABLE attendance_new (
            {', '.join(keep_columns)}
        )
        """
        
        cursor.execute(new_table_sql)
        
        # Copy data from old table to new table (excluding regularization columns)
        old_columns = [col[1] for col in columns if 'regularization' not in col[1]]
        copy_sql = f"""
        INSERT INTO attendance_new ({', '.join(old_columns)})
        SELECT {', '.join(old_columns)} FROM attendance
        """
        
        cursor.execute(copy_sql)
        
        # Drop old table and rename new table
        cursor.execute("DROP TABLE attendance")
        cursor.execute("ALTER TABLE attendance_new RENAME TO attendance")
        
        print("✅ Regularization columns removed from attendance table")
        
        # 4. Commit all changes
        conn.commit()
        print("\n✅ All database cleanup operations completed successfully!")
        
        # 5. Verify cleanup
        print("\n🔍 Verifying cleanup...")
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name LIKE '%regularization%' OR name LIKE '%notification%')")
        remaining_tables = cursor.fetchall()
        
        if remaining_tables:
            print(f"⚠️  Warning: Some tables still exist: {[t[0] for t in remaining_tables]}")
        else:
            print("✅ All regularization and notification tables removed")
        
        # Check attendance columns
        cursor.execute('PRAGMA table_info(attendance)')
        attendance_columns = cursor.fetchall()
        reg_columns = [col[1] for col in attendance_columns if 'regularization' in col[1]]
        
        if reg_columns:
            print(f"⚠️  Warning: Some regularization columns still exist: {reg_columns}")
        else:
            print("✅ All regularization columns removed from attendance table")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

def main():
    """Main function"""
    print("🗄️  VishnoRex Database Cleanup Tool")
    print("=" * 60)
    print("This will remove:")
    print("- attendance_regularization_requests table")
    print("- notifications table") 
    print("- regularization_* columns from attendance table")
    print()
    
    # Check if database exists
    if not os.path.exists('vishnorex.db'):
        print("❌ Database file 'vishnorex.db' not found!")
        return
    
    # Confirm cleanup
    confirm = input("Do you want to proceed with cleanup? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ Cleanup cancelled by user")
        return
    
    # Perform cleanup
    success = cleanup_database()
    
    if success:
        print("\n🎉 Database cleanup completed successfully!")
        print("📝 A backup was created before cleanup")
        print("🚀 The system is now ready without regularization and notification features")
    else:
        print("\n💥 Database cleanup failed!")
        print("📝 Please check the backup file and try again")

if __name__ == "__main__":
    main()
