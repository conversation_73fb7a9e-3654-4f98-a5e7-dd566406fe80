#!/usr/bin/env python3
"""
Setup demo school for testing the simplified application
"""

import sqlite3
from database import init_db
from flask import Flask

# Create a minimal Flask app for database initialization
app = Flask(__name__)
app.secret_key = 'demo_key'

# Initialize database
with app.app_context():
    init_db(app)

# Add demo school
db_path = 'vishnorex.db'
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Check if demo school already exists
existing = cursor.execute('SELECT id FROM schools WHERE name = ?', ('Demo School',)).fetchone()

if not existing:
    # Insert demo school
    cursor.execute('''
        INSERT INTO schools (name, address, contact_email, contact_phone)
        VALUES (?, ?, ?, ?)
    ''', ('Demo School', '123 Demo Street, Demo City', '<EMAIL>', '******-0123'))
    
    school_id = cursor.lastrowid
    print(f"Demo school created with ID: {school_id}")
else:
    print(f"Demo school already exists with ID: {existing[0]}")

conn.commit()
conn.close()

print("Demo setup complete!")
print("You can now:")
print("1. Run 'python app.py' to start the server")
print("2. Open http://localhost:5000 in your browser")
print("3. Select 'Demo School' and register users")
print("4. Test ESSL fingerprint integration")
