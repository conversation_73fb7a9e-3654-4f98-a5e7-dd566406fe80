# Simplified User Registration & ESSL Fingerprint System

A streamlined system focused on user registration and ESSL fingerprint scanner integration.

## 🚀 Core Features

- **User Registration**: Simple user registration with essential information
- **ESSL Fingerprint Integration**: Direct integration with ESSL ZK biometric devices
- **Biometric Enrollment**: Enroll users on fingerprint devices
- **Verification Testing**: Test biometric verification functionality

## 🛠️ Installation

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application**
   ```bash
   python app.py
   ```

3. **Access the application**
   Open your browser and go to `http://127.0.0.1:5000`

## 📁 Project Structure

```
├── app.py                 # Main Flask application
├── database.py           # Database configuration
├── zk_biometric.py       # ESSL device integration
├── requirements.txt      # Python dependencies
├── templates/
│   ├── index.html        # Home page
│   └── register.html     # Registration page
└── static/
    ├── images/           # Logo and images
    └── uploads/          # User photo uploads
```

## 🔧 Configuration

### ESSL Device Setup

1. Connect your ESSL ZK biometric device to the network
2. Configure the device IP address (default: *************)
3. Ensure the device is accessible from the server

## 🎯 Usage

### User Registration

1. Access the home page at `/`
2. Select an organization
3. Click "Register New User"
4. Fill in user details and submit
5. Proceed to biometric enrollment

### Biometric Enrollment

1. After successful registration, enroll fingerprint data
2. Use the device interface to capture biometric data
3. Test verification to ensure enrollment was successful

## 🔌 ESSL Device Integration

### Supported Devices
- ESSL ZK devices with Ethernet connectivity
- Any ZK-compatible biometric device

### Device Configuration
- **Device IP**: ************* (default)
- **Port**: 4370
- **Connection**: Direct TCP/IP

## 📊 API Endpoints

### Core Functions
- `GET /` - Home page with organization selection
- `GET /register` - User registration form
- `POST /register` - Submit user registration
- `POST /enroll_biometric` - Enroll user on biometric device
- `POST /verify_biometric` - Test biometric verification
- `POST /check_device_verification` - Check for recent device verification
- `GET /get_staff_list` - View registered staff
- `POST /add_school` - Add new organization

## 🔧 Troubleshooting

### Device Connection Issues
- Verify device IP address and network connectivity
- Check device power and network settings
- Ensure firewall allows communication on port 4370

### Registration Issues
- Check database permissions
- Verify all required fields are filled
- Ensure organization is selected

## 📝 License

This project is a simplified version focused on core functionality.
