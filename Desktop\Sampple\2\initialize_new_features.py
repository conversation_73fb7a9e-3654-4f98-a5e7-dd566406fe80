#!/usr/bin/env python3
"""
Initialize the new On Duty and Permission features
"""

import sqlite3
import os
from database import init_db
from flask import Flask

def initialize_new_features():
    """Initialize the new on-duty and permission features"""
    print("🚀 Initializing On Duty and Permission Features")
    print("=" * 50)
    
    # Create a temporary Flask app to initialize the database
    app = Flask(__name__)
    
    try:
        # Initialize the database with new tables
        with app.app_context():
            init_db(app)
        
        print("✅ Database tables created successfully")
        
        # Verify the new tables exist
        conn = sqlite3.connect('vishnorex.db')
        cursor = conn.cursor()
        
        # Check if on_duty_applications table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='on_duty_applications'")
        if cursor.fetchone():
            print("✅ on_duty_applications table created")
        else:
            print("❌ on_duty_applications table not found")
        
        # Check if permission_applications table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='permission_applications'")
        if cursor.fetchone():
            print("✅ permission_applications table created")
        else:
            print("❌ permission_applications table not found")
        
        # Show table structures
        print("\n📊 Table Structures:")
        
        print("\n--- on_duty_applications ---")
        cursor.execute("PRAGMA table_info(on_duty_applications)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        print("\n--- permission_applications ---")
        cursor.execute("PRAGMA table_info(permission_applications)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        
        print("\n🎉 New Features Initialized Successfully!")
        print("\nFeatures Added:")
        print("1. ✅ On Duty Applications")
        print("   - Staff can apply for official duties")
        print("   - Admin can approve/reject applications")
        print("   - Supports duty types: Official Work, Training, Meeting, Conference, Field Work, Other")
        print("   - Includes location, purpose, and time details")
        
        print("\n2. ✅ Permission Applications")
        print("   - Staff can apply for short-term permissions")
        print("   - Admin can approve/reject applications")
        print("   - Supports permission types: Personal Work, Medical, Emergency, Family Function, Other")
        print("   - Calculates duration automatically")
        
        print("\n📋 How to Use:")
        print("1. Staff Dashboard:")
        print("   - Click 'Apply On Duty' button to request official duty")
        print("   - Click 'Apply Permission' button to request permission")
        print("   - View application status in respective sections")
        
        print("\n2. Admin Dashboard:")
        print("   - View pending applications in separate sections")
        print("   - Approve/reject applications with optional remarks")
        print("   - Applications are processed in real-time")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing features: {e}")
        return False

def create_sample_data():
    """Create some sample data for testing"""
    print("\n🧪 Creating Sample Data for Testing")
    print("=" * 40)
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Get a staff member for testing
        cursor.execute('SELECT id, full_name FROM staff LIMIT 1')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff members found. Please add staff first.")
            return False
        
        staff_id, staff_name = staff
        print(f"Using staff: {staff_name} (ID: {staff_id})")
        
        # Create sample on-duty application
        cursor.execute('''
            INSERT INTO on_duty_applications 
            (staff_id, school_id, duty_type, start_date, end_date, start_time, end_time, location, purpose, reason, status)
            VALUES (?, 1, 'Training', '2025-07-20', '2025-07-20', '09:00', '17:00', 'Training Center', 'Attend software training workshop', 'Professional development', 'pending')
        ''', (staff_id,))
        
        # Create sample permission application
        cursor.execute('''
            INSERT INTO permission_applications 
            (staff_id, school_id, permission_type, permission_date, start_time, end_time, duration_hours, reason, status)
            VALUES (?, 1, 'Medical', '2025-07-18', '14:00', '16:00', 2.0, 'Doctor appointment for routine checkup', 'pending')
        ''', (staff_id,))
        
        conn.commit()
        
        print("✅ Sample data created:")
        print("  - 1 On-duty application (Training)")
        print("  - 1 Permission application (Medical)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = initialize_new_features()
    
    if success:
        create_sample = input("\nWould you like to create sample data for testing? (y/N): ").strip().lower()
        if create_sample == 'y':
            create_sample_data()
        
        print("\n🚀 Ready to Use!")
        print("Start the application with: python app.py")
        print("Then login as staff or admin to test the new features.")
    else:
        print("\n❌ Initialization failed. Please check the errors above.")
