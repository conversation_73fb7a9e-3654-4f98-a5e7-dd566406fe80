#!/usr/bin/env python3
"""
Test to verify the "No item with that key" error is resolved
"""

import sqlite3
import datetime
import sys
import os

# Add current directory to path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_weekly_attendance_route():
    """Test the get_weekly_attendance route with on-duty records"""
    print("=== Testing Weekly Attendance Route ===")
    
    # Import the function from app
    try:
        from app import calculate_daily_attendance_data
        print("✅ Successfully imported calculate_daily_attendance_data function")
    except ImportError as e:
        print(f"❌ Failed to import function: {e}")
        return False
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get staff with on-duty record
        cursor.execute('''
            SELECT DISTINCT s.id, s.full_name
            FROM staff s
            INNER JOIN attendance a ON s.id = a.staff_id
            WHERE a.status = 'on_duty'
            LIMIT 1
        ''')
        staff = cursor.fetchone()
        
        if not staff:
            print("⚠️  No staff with on-duty records found, creating test record...")
            
            # Get a staff member
            cursor.execute('SELECT id, full_name FROM staff LIMIT 1')
            staff = cursor.fetchone()
            
            if not staff:
                print("❌ No staff members found")
                return False
            
            staff_id = staff['id']
            test_date = '2025-07-29'
            
            # Create test on-duty record
            cursor.execute('''
                INSERT OR REPLACE INTO attendance 
                (staff_id, school_id, date, status, on_duty_type, on_duty_location, on_duty_purpose)
                VALUES (?, 1, ?, 'on_duty', 'Testing', 'Test Location', 'Testing key error fix')
            ''', (staff_id, test_date))
            
            conn.commit()
            print(f"✅ Created test on-duty record for staff {staff['full_name']}")
        
        staff_id = staff['id']
        staff_name = staff['full_name']
        print(f"Testing with staff: {staff_name} (ID: {staff_id})")
        
        # Test the updated query with on-duty columns
        week_start = datetime.date(2025, 7, 21)
        week_end = week_start + datetime.timedelta(days=6)
        
        cursor.execute('''
            SELECT date, time_in, time_out, overtime_in, overtime_out, status,
                   late_duration_minutes, early_departure_minutes,
                   on_duty_type, on_duty_location, on_duty_purpose
            FROM attendance
            WHERE staff_id = ? AND date BETWEEN ? AND ?
            ORDER BY date
        ''', (staff_id, week_start, week_end))
        
        attendance_records = cursor.fetchall()
        
        print(f"✅ Found {len(attendance_records)} attendance records for the week")
        
        # Test calculate_daily_attendance_data with each record
        shift_def = {
            'start_time': '09:20:00',
            'end_time': '16:30:00',
            'grace_period_minutes': 10
        }
        
        for record in attendance_records:
            try:
                print(f"\nTesting record for {record['date']}:")
                print(f"  - Status: {record['status']}")
                
                # Test the function
                day_data = calculate_daily_attendance_data(
                    datetime.date.today(),
                    record,
                    shift_def,
                    'general'
                )
                
                print(f"  - Function result: {day_data['present_status']}")
                
                if day_data['present_status'] == 'On Duty':
                    print(f"  - On-duty type: {day_data.get('on_duty_type', 'N/A')}")
                    print(f"  - On-duty location: {day_data.get('on_duty_location', 'N/A')}")
                    print(f"  - On-duty purpose: {day_data.get('on_duty_purpose', 'N/A')}")
                
                print("  ✅ Record processed successfully")
                
            except Exception as e:
                print(f"  ❌ Error processing record: {e}")
                print(f"  Error type: {type(e).__name__}")
                return False
        
        print("\n✅ All attendance records processed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        conn.close()

def test_staff_profile_route():
    """Test the staff profile route with on-duty data"""
    print("\n=== Testing Staff Profile Route ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get staff with on-duty record
        cursor.execute('''
            SELECT DISTINCT s.id, s.full_name
            FROM staff s
            INNER JOIN attendance a ON s.id = a.staff_id
            WHERE a.status = 'on_duty'
            LIMIT 1
        ''')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff with on-duty records found")
            return False
        
        staff_id = staff['id']
        staff_name = staff['full_name']
        print(f"Testing staff profile for: {staff_name} (ID: {staff_id})")
        
        # Test the updated query with on-duty columns
        thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).date()
        cursor.execute('''
            SELECT date, time_in, time_out, overtime_in, overtime_out, status,
                   on_duty_type, on_duty_location, on_duty_purpose
            FROM attendance
            WHERE staff_id = ? AND date >= ?
            ORDER BY date DESC
        ''', (staff_id, thirty_days_ago))
        
        attendance_records = cursor.fetchall()
        
        print(f"✅ Found {len(attendance_records)} attendance records (last 30 days)")
        
        # Test accessing all fields
        on_duty_count = 0
        for record in attendance_records:
            try:
                status = record['status'] if record['status'] else 'absent'
                
                if status == 'on_duty':
                    on_duty_count += 1
                    # Test accessing on-duty fields
                    on_duty_type = record['on_duty_type'] if record['on_duty_type'] else 'Official Work'
                    on_duty_location = record['on_duty_location'] if record['on_duty_location'] else 'Not specified'
                    on_duty_purpose = record['on_duty_purpose'] if record['on_duty_purpose'] else 'Official duty'
                    
                    print(f"  - {record['date']}: {status}")
                    print(f"    Type: {on_duty_type}")
                    print(f"    Location: {on_duty_location}")
                    print(f"    Purpose: {on_duty_purpose[:50]}...")
                
            except Exception as e:
                print(f"❌ Error accessing record fields: {e}")
                return False
        
        print(f"✅ Found {on_duty_count} on-duty records, all fields accessible")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

def test_defensive_programming():
    """Test the defensive programming in calculate_daily_attendance_data"""
    print("\n=== Testing Defensive Programming ===")
    
    # Import the function from app
    try:
        from app import calculate_daily_attendance_data
        print("✅ Successfully imported calculate_daily_attendance_data function")
    except ImportError as e:
        print(f"❌ Failed to import function: {e}")
        return False
    
    # Create a mock attendance record without on-duty fields
    class MockRecord:
        def __init__(self, data):
            self.data = data
        
        def __getitem__(self, key):
            if key in self.data:
                return self.data[key]
            else:
                raise KeyError(f"No item with that key: {key}")
    
    # Test with missing on-duty fields
    mock_record = MockRecord({
        'status': 'on_duty',
        'time_in': None,
        'time_out': None,
        'overtime_in': None,
        'overtime_out': None,
        'late_duration_minutes': None,
        'early_departure_minutes': None
        # Missing: on_duty_type, on_duty_location, on_duty_purpose
    })
    
    shift_def = {
        'start_time': '09:20:00',
        'end_time': '16:30:00',
        'grace_period_minutes': 10
    }
    
    try:
        print("Testing with missing on-duty fields...")
        day_data = calculate_daily_attendance_data(
            datetime.date.today(),
            mock_record,
            shift_def,
            'general'
        )
        
        print("✅ Function handled missing fields gracefully")
        print(f"  - Status: {day_data['present_status']}")
        print(f"  - On-duty type: {day_data.get('on_duty_type', 'N/A')}")
        print(f"  - On-duty location: {day_data.get('on_duty_location', 'N/A')}")
        print(f"  - On-duty purpose: {day_data.get('on_duty_purpose', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Function failed with missing fields: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing 'No item with that key' Error Fix")
    print("=" * 60)
    
    results = []
    
    # Run tests
    results.append(("Weekly Attendance Route", test_weekly_attendance_route()))
    results.append(("Staff Profile Route", test_staff_profile_route()))
    results.append(("Defensive Programming", test_defensive_programming()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 'NO ITEM WITH THAT KEY' ERROR RESOLVED!")
        print("\nFixed issues:")
        print("1. ✅ Added missing on-duty columns to database queries")
        print("2. ✅ Added defensive programming for missing fields")
        print("3. ✅ Weekly attendance route updated with on-duty columns")
        print("4. ✅ Staff profile route updated with on-duty columns")
        print("5. ✅ Function handles missing keys gracefully")
        
        print("\n🚀 On-duty functionality should now work without key errors!")
    else:
        print(f"\n⚠️  {total - passed} issues still need attention")
        print("Please check the failed tests above.")
