#!/usr/bin/env python3
"""
Simple Database Check for Biometric Verifications

Check biometric_verifications table structure and data
"""

import sqlite3
import os

def check_biometric_table():
    """Check biometric_verifications table"""
    
    if not os.path.exists('vishnorex.db'):
        print("❌ Database file 'vishnorex.db' not found!")
        return
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()

    try:
        # Check if biometric_verifications table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='biometric_verifications'")
        table_exists = cursor.fetchone()

        if table_exists:
            print('✅ biometric_verifications table exists')
            
            # Check table structure
            cursor.execute('PRAGMA table_info(biometric_verifications)')
            columns = cursor.fetchall()
            print('\n📊 Table structure:')
            for col in columns:
                print(f'  - {col[1]} ({col[2]})')
            
            # Check if there's any data
            cursor.execute('SELECT COUNT(*) FROM biometric_verifications')
            count = cursor.fetchone()[0]
            print(f'\n📈 Total records: {count}')
            
            if count > 0:
                # Show recent records
                cursor.execute('SELECT * FROM biometric_verifications ORDER BY verification_time DESC LIMIT 5')
                recent = cursor.fetchall()
                print('\n📋 Recent records:')
                for record in recent:
                    print(f'  - Staff ID: {record[1]}, Type: {record[3]}, Time: {record[4]}')
        else:
            print('❌ biometric_verifications table does not exist')

    except Exception as e:
        print(f"❌ Error checking database: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_biometric_table()
