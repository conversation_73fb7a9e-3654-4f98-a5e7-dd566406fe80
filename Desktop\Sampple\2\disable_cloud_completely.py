#!/usr/bin/env python3
"""
Disable Cloud Functionality Completely
Stop WebSocket errors and focus only on Ethernet connection
"""

import json
import os
import shutil

def backup_current_config():
    """Backup current configuration"""
    print("💾 Backing up current configuration...")
    
    if os.path.exists('cloud_config.json'):
        shutil.copy('cloud_config.json', 'cloud_config_backup.json')
        print("✅ Backed up to cloud_config_backup.json")
    
    return True

def create_ethernet_only_config():
    """Create completely disabled cloud configuration"""
    print("🔌 Creating Ethernet-only configuration...")
    
    ethernet_config = {
        "config": {
            "cloud_provider": "disabled",
            "api_base_url": "",
            "websocket_url": "",
            "mqtt_broker": "",
            "mqtt_port": 0,
            "api_key": "",
            "secret_key": "",
            "organization_id": "",
            "connection_timeout": 30,
            "retry_attempts": 0,
            "heartbeat_interval": 0,
            "use_ssl": False,
            "verify_ssl": False,
            "encryption_enabled": False,
            "auto_sync": False,
            "sync_interval": 0,
            "batch_size": 0,
            "local_backup": True,
            "backup_retention_days": 30
        },
        "devices": [
            {
                "device_id": "181",
                "device_name": "ZK Biometric Device 181",
                "device_type": "ZK_BIOMETRIC",
                "local_ip": "*************",
                "local_port": 32150,
                "cloud_enabled": False,
                "sync_interval": 0,
                "last_sync": None
            },
            {
                "device_id": "1",
                "device_name": "ZK Factory Default Device",
                "device_type": "ZK_BIOMETRIC", 
                "local_ip": "*************",
                "local_port": 4370,
                "cloud_enabled": False,
                "sync_interval": 0,
                "last_sync": None
            }
        ],
        "endpoints": []
    }
    
    try:
        with open('cloud_config.json', 'w') as f:
            json.dump(ethernet_config, f, indent=2)
        
        print("✅ Ethernet-only configuration saved")
        return True
        
    except Exception as e:
        print(f"❌ Error creating config: {e}")
        return False

def modify_app_for_ethernet_only():
    """Create a modified app.py that disables cloud functionality"""
    print("🔧 Creating Ethernet-only app configuration...")
    
    try:
        # Read current app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Create modified version
        modified_content = app_content
        
        # Add cloud disable flag at the top
        cloud_disable_code = '''
# DISABLE CLOUD FUNCTIONALITY COMPLETELY
import os
os.environ['DISABLE_CLOUD'] = '1'
CLOUD_ENABLED = False

'''
        
        # Insert after imports
        import_end = modified_content.find('from flask import')
        if import_end != -1:
            line_end = modified_content.find('\n', import_end)
            modified_content = modified_content[:line_end+1] + cloud_disable_code + modified_content[line_end+1:]
        
        # Save modified version
        with open('app_ethernet_only.py', 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print("✅ Created app_ethernet_only.py")
        return True
        
    except Exception as e:
        print(f"❌ Error modifying app: {e}")
        return False

def create_startup_script():
    """Create a startup script that ensures cloud is disabled"""
    print("📝 Creating startup script...")
    
    startup_script = '''#!/usr/bin/env python3
"""
Ethernet-Only Startup Script
Starts the application with cloud functionality completely disabled
"""

import os
import sys

# Force disable cloud functionality
os.environ['DISABLE_CLOUD'] = '1'
os.environ['CLOUD_ENABLED'] = 'False'

print("🔌 Starting application in ETHERNET-ONLY mode")
print("✅ Cloud functionality disabled")
print("✅ WebSocket connections disabled")
print("=" * 50)

# Import and run the app
if __name__ == '__main__':
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ Error starting app: {e}")
        print("Try running: python app.py")
'''
    
    try:
        with open('start_ethernet_only.py', 'w') as f:
            f.write(startup_script)
        
        print("✅ Created start_ethernet_only.py")
        return True
        
    except Exception as e:
        print(f"❌ Error creating startup script: {e}")
        return False

def test_ethernet_connection():
    """Test direct Ethernet connection"""
    print("\n🧪 Testing Ethernet Connection...")
    
    import socket
    
    devices = [
        {"ip": "*************", "port": 32150, "name": "Primary Device"},
        {"ip": "*************", "port": 4370, "name": "Factory Default"},
        {"ip": "*************", "port": 80, "name": "Web Interface"}
    ]
    
    working_devices = []
    
    for device in devices:
        try:
            print(f"Testing {device['name']} ({device['ip']}:{device['port']})...", end=" ")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((device['ip'], device['port']))
            sock.close()
            
            if result == 0:
                print("✅ ACCESSIBLE")
                working_devices.append(device)
            else:
                print("❌ Not accessible")
                
        except Exception as e:
            print(f"❌ Error")
    
    return working_devices

def provide_solution_steps():
    """Provide step-by-step solution"""
    print("\n📋 SOLUTION STEPS TO STOP THE ERRORS")
    print("=" * 60)
    
    print("1. 🛑 STOP the current Flask application (Ctrl+C)")
    print()
    
    print("2. 🔌 START with Ethernet-only mode:")
    print("   Option A: python start_ethernet_only.py")
    print("   Option B: python app.py (with new config)")
    print()
    
    print("3. 🌐 Configure device via web interface:")
    print("   - Open: http://*************")
    print("   - Login: admin / 123456")
    print("   - Find Communication/Network settings")
    print("   - Set Device ID: 181")
    print("   - Set Common Key: 1302")
    print("   - Set Port: 32150")
    print("   - Enable TCP/IP")
    print("   - Save and restart device")
    print()
    
    print("4. 🧪 Test connection:")
    print("   - python simple_test_181.py")
    print("   - Should work without WebSocket errors")

def main():
    """Main solution function"""
    print("🔧 COMPLETE CLOUD DISABLE SOLUTION")
    print("=" * 60)
    print("Stopping WebSocket 403 errors and focusing on Ethernet")
    print("=" * 60)
    
    # Step 1: Backup current config
    backup_current_config()
    
    # Step 2: Create ethernet-only config
    if create_ethernet_only_config():
        print("✅ Cloud functionality disabled in config")
    
    # Step 3: Create startup script
    if create_startup_script():
        print("✅ Ethernet-only startup script created")
    
    # Step 4: Test connectivity
    working_devices = test_ethernet_connection()
    
    print(f"\n✅ Found {len(working_devices)} accessible devices:")
    for device in working_devices:
        print(f"   - {device['name']}: {device['ip']}:{device['port']}")
    
    # Step 5: Provide solution steps
    provide_solution_steps()
    
    print("\n🎯 SUMMARY:")
    print("✅ Cloud functionality completely disabled")
    print("✅ WebSocket connections will be stopped")
    print("✅ Application will focus only on Ethernet")
    print("✅ Configuration files updated")
    
    print("\n⚠️ IMPORTANT:")
    print("1. STOP your current Flask app (Ctrl+C)")
    print("2. START with: python start_ethernet_only.py")
    print("3. Configure device via web interface")

if __name__ == '__main__':
    main()
