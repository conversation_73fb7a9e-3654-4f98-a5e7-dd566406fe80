#!/usr/bin/env python3
"""
Setup initial passwords for staff members
This allows staff to login with their Staff ID as password initially,
then they can change it to their preferred password
"""

import sqlite3
from werkzeug.security import generate_password_hash

def setup_initial_passwords():
    """Set initial passwords for staff members using their Staff ID"""
    
    print("=== Setting Up Initial Passwords ===")
    print("This will set each staff member's password to their Staff ID")
    print("Staff can then login and change to their preferred password")
    print()
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get all staff
        cursor.execute('SELECT id, staff_id, full_name FROM staff')
        all_staff = cursor.fetchall()
        
        if not all_staff:
            print("❌ No staff found in database")
            return
        
        print(f"Found {len(all_staff)} staff members:")
        
        updated_count = 0
        for staff in all_staff:
            # Use Staff ID as initial password
            initial_password = staff['staff_id']
            password_hash = generate_password_hash(initial_password)
            
            # Update password
            cursor.execute('''
                UPDATE staff SET password_hash = ? WHERE id = ?
            ''', (password_hash, staff['id']))
            
            print(f"✅ {staff['full_name']} (Staff ID: {staff['staff_id']}) - Password: {initial_password}")
            updated_count += 1
        
        conn.commit()
        
        print(f"\n🎉 Successfully set initial passwords for {updated_count} staff members")
        print("\nINSTRUCTIONS FOR STAFF:")
        print("1. Login using your Staff ID as both username AND password")
        print("2. Go to your profile dropdown → 'Change Password'")
        print("3. Set your preferred password")
        print("4. Use your new password for future logins")
        
        print(f"\nCURRENT LOGIN CREDENTIALS:")
        for staff in all_staff:
            print(f"   {staff['full_name']}: Username={staff['staff_id']}, Password={staff['staff_id']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        conn.close()

def reset_to_staff_id_passwords():
    """Reset all passwords to Staff ID (for testing)"""
    
    choice = input("\nDo you want to reset all passwords to Staff ID? (y/N): ").strip().lower()
    if choice != 'y':
        print("Operation cancelled")
        return
    
    setup_initial_passwords()

if __name__ == '__main__':
    print("=== Staff Password Setup Tool ===")
    print()
    print("This tool helps set up initial passwords for staff members.")
    print("Each staff member will be able to login with their Staff ID")
    print("and then change to their preferred password.")
    print()
    
    setup_initial_passwords()
    
    print("\n" + "="*60)
    print("NEXT STEPS:")
    print("1. Start your Flask app: python app.py")
    print("2. Staff can login with Staff ID as password")
    print("3. Staff should immediately change to their preferred password")
    print("4. Future logins use the new password")
    print()
    print("EXAMPLE:")
    print("- Staff 'Navanee' with Staff ID '333'")
    print("- Initial login: Username=333, Password=333")
    print("- After password change: Username=333, Password=<their choice>")
