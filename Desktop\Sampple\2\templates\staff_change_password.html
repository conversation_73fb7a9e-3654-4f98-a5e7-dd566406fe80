<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - Staff Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .password-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            background: white;
        }
        .password-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .password-input {
            position: relative;
        }
        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }
        .btn-change-password {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .password-requirements {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <div class="container">
        <div class="password-container">
            <div class="password-header">
                <i class="fas fa-key fa-3x text-primary mb-3"></i>
                <h2>Change Password</h2>
                <p class="text-muted">Update your account password</p>
            </div>

            <form id="changePasswordForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                
                <div class="form-group">
                    <label for="current_password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Current Password
                    </label>
                    <div class="password-input">
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('current_password')"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="new_password" class="form-label">
                        <i class="fas fa-key me-2"></i>New Password
                    </label>
                    <div class="password-input">
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('new_password')"></i>
                    </div>
                    <div class="password-requirements">
                        Password must be at least 6 characters long
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirm_password" class="form-label">
                        <i class="fas fa-check-circle me-2"></i>Confirm New Password
                    </label>
                    <div class="password-input">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('confirm_password')"></i>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-change-password">
                    <i class="fas fa-save me-2"></i>Change Password
                </button>
            </form>

            <div class="back-link">
                <a href="{{ url_for('staff_dashboard') }}" class="text-decoration-none">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert">
            <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="successMessage"></div>
        </div>

        <div id="errorToast" class="toast" role="alert">
            <div class="toast-header bg-danger text-white">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="errorMessage"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = field.nextElementSibling;
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        function showToast(type, message) {
            const toast = document.getElementById(type + 'Toast');
            const messageElement = document.getElementById(type + 'Message');
            messageElement.textContent = message;
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            
            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changing Password...';
            
            fetch('/staff/change_password', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    // Clear form
                    this.reset();
                    // Redirect to dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = '/staff/dashboard';
                    }, 2000);
                } else {
                    showToast('error', data.error);
                }
            })
            .catch(error => {
                showToast('error', 'An error occurred. Please try again.');
                console.error('Error:', error);
            })
            .finally(() => {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Change Password';
            });
        });

        // Password strength indicator
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            const requirements = this.parentElement.nextElementSibling;
            
            if (password.length >= 6) {
                requirements.style.color = '#28a745';
                requirements.innerHTML = '<i class="fas fa-check me-1"></i>Password meets requirements';
            } else {
                requirements.style.color = '#6c757d';
                requirements.innerHTML = 'Password must be at least 6 characters long';
            }
        });

        // Confirm password validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });
    </script>
</body>
</html>
